from po_lib.constants import TxnType, ProductId

from po_lib.constants import Integrations, PhoneNumberType
from po_lib.utils import is_possible_number

from .manageentity_dialog_dm_fpe import fpe_specs as fpe_specs_dm
from .manageentity_dialog_astro_fpe import fpe_specs as fpe_specs_astro
from .manageentity_dialog_savedcards_fpe import fpe_specs as fpe_specs_savedcards
from .manageentity_dialog_mfg_media_fpe import fpe_specs as fpe_mfg_media

include_specs = [fpe_specs_dm, fpe_specs_astro, fpe_specs_savedcards, fpe_mfg_media]


def addphonetypeid(params, callback, _):
    numbers = params["numbers"]

    for i in numbers:
        if i.get("numt_contact_number_type") == "Home":
            i["phone_id"] = -2
        elif i.get("numt_contact_number_type") == "Office":
            i["phone_id"] = -4
        elif i.get("numt_contact_number_type") == "Fax":
            i["phone_id"] = -5
        elif i.get("numt_contact_number_type") == "Mobile":
            i["phone_id"] = -1
    callback(numbers)


def checkphonetypeid(params, callback, _):
    # Prevents impossible phone numbers in output
    new_ids = []
    new_num = []
    new_num_id = []
    primary_list = []

    num_types = {
        "Home": PhoneNumberType.HOME,
        "Mobile": PhoneNumberType.MOBILE,
        "Office": PhoneNumberType.OFFICE,
        "Fax": PhoneNumberType.FAX
    }

    for num, num_id, num_type in zip(params["numbers"], params["number_id"], params["ids"]):
        if is_possible_number(num) and num_type in num_types:
            new_ids.append(num_types.get(num_type))
            new_num.append(num)
            new_num_id.append(num_id if num_id is not None else "@null")

    if new_ids:
        primary_list = [True] + [False] * (len(new_ids) - 1)

    new_ids = ",".join(map(str, new_ids))
    new_num = ",".join(map(str, new_num))
    new_num_id = ",".join(map(str, new_num_id))

    callback([new_ids, new_num, new_num_id, primary_list])


def checkemailtypeid(params, callback, _):
    emails = params["emails"]
    email_id = params["email_id"]
    email_type_id = params["email_type_id"]
    new_ids = ""
    new_email = ""
    new_email_id = ""
    primary_list = []

    for i in range(len(emails)):
        if emails[i] != "" and emails[i] != None:
            new_ids += str(email_type_id[i])
            new_email += emails[i]
            if email_id[i] is not None:
                new_email_id += str(email_id[i])
            elif email_id[i] is None and emails[i] is not None:
                new_email_id += "@null"
            if len(primary_list) == 0:
                primary_list.append(True)
            else:
                primary_list.append(False)

            if new_ids != "":
                new_ids += ","
                new_email_id += ","
                new_email += ","

    callback([new_ids[:-1], new_email[:-1], new_email_id[:-1], primary_list])


fpe_specs = {
    "fpe_actions": {
        "disable_buttons": [
            ("change_state", "grp_command_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_brand_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_customer_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_paperwork_breeder_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_paperwork_broker_buttons", {"default_state": "disabled"}),
            ("change_state", "grp_paperwork_trans_buttons", {"default_state": "disabled"}),
            ("change_state", "g_managesupplierinfo_button", {"default_state": "disabled"}),
            ("change_state", "g_manageemergencycontact_button", {"default_state": "disabled"}),
            ("change_state", "g_deleteemergencycontact_button", {"default_state": "disabled"}),
            ("change_state", "grp_disabled_phase_1", {"default_state": "disabled"}),

            "$grp_layaway_buttons.hide",
         ],

        "enable_buttons": ("change_state", "grp_command_buttons", {"default_state": "enabled"}),

        "show_error": [
            ("error_message", "'\n'.join($errors)"),
            ("change_state", "grp_command_buttons", {"default_state": "enabled"})
        ],

        "setup_form": [
            ("if", "$app.lite == True",
             [
                 ("change_state", "app_lite_disable", {"default_state": "disabled"}),
                 "$g_manageentity_tabwidget.hide_tab('g_address_tab')",
                 "$app_lite_hide.hide",
                 "$app_lite_show.show",
             ],
             [
                 "$app_lite_hide.show",
                 "$app_lite_show.hide",
             ])
        ],

        "prepare_form": [
            "$form.set_width(990)",
            "$form.set_height(680)",
            "$g_salestab_tabWidget.hide_tab('g_specialorders_tab')",
            "$g_salestab_tabWidget.hide_tab('g_recurring_tab')",
            "$g_manageentity_tabwidget.hide_tab('g_astroinfo_widget')",
            "$g_manageentity_tabwidget.hide_tab('g_manurfacturers_tab')",
            "$g_manageentity_tabwidget.hide_tab('g_savedcards_widget')",
            "$g_manageentity_tabwidget.hide_tab('g_website_tab')",

            ("change_state", "g_resetpassword_button", {"default_state": "disabled"}),
            ("change_state", "g_webusername_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_deletecustomerpet_button", {"default_state": "disabled"}),
            ("change_state", "g_manage_pet_button", {"default_state": "disabled"}),
            ("change_state", "g_managefrequentBuyer_button", {"default_state": "disabled"}),

            # Load App Settings and Set Permissions
            ("push", "$storage.session", "$app.get_session_data"),
            ("push", "$storage.location_id", "$app.get_session_data.location_id"),
            ("push", "$storage.user_id", "$app.get_session_data.user_id"),
            ("push", "$storage.device_id", "$app.get_session_data.device_id"),
            ("push", "$storage.location_name", "$app.permissions.entity_location.ent_location.loc_report_code"),
            # End Load App Settings and Set Permissions

            ("push", "$storage.corp", "$form.input.corp"),
            "set_permissions",
            # Featured
            ("if", "$ext.releases.config['custompricing'] == 'true' and $app.name == 'Cash Register'",
                "$g_custompricing_group.show",
                "$g_custompricing_group.hide"),
            ("if", "$ext.releases.config['subscription'] == 'true' and $app.name == 'Cash Register'",
                "$g_subscription_group.show",
                "$g_subscription_group.hide"),

            ("if", "$app.name == 'Cash Register' or $app.name == 'House Accounts'",
                [
                    ("if", "$app.lite != True", "$g_houseaccount_group.show"),
                    "$g_taxes_groupbox.show",

                ],
                [
                    "$g_custompricing_label.hide"
                    "$g_taxes_groupbox.hide"
                ]),

            ("if", "$app.name == 'Cash Register'",
             [

                 "$g_quotes_select_button.show",
             ],
             [
                 "$g_custompricing_label.hide"
                 "$g_updatetaxcertificates_label.hide"
             ]),

            # ("if", "$app.name != 'Pet Tracker'", "$g_manage_pet_button.hide"),
            ("if", "$app.name != 'Cash Register'", "$g_display_layaway_button.hide"),

            ("push", "$storage.status", "$form.input.flags"),
            ("push", "$storage.id", ''),
            ("push", "$storage.loc_entity_id", ''),
            ("push", "$storage.sender", "None"),
            ("push", "$storage.entity_info", "None"),
            ("push", "$storage.pricing_group_id", 0),
            ("push", "$storage.use_pidb", False),
            ("push", "$storage.roles", []),
            ("push", "$storage.duplicate_number", None),
            ("push", "$storage.entity_roles", []),
            ("push", "$storage.loading", True),

            ("push", "$storage.errors", None),

            ("push", "$storage.period", None),

            "$g_leadtime_lineedit.set_input('int')",
            "$g_orderfrequency_lineedit.set_input('int')",
            # "$g_entityfirst_lineedit.set_input('alphanum')",
            # "$g_entitylast_lineedit.set_input('alphanum')",
            # "$g_business_lineedit.set_input('alphanum')",


            ("push", "$storage.id_list", []),

            ("change_state", "g_pidbmappedto_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_lastsync_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_pidbstatus_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_pidbnote_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_loyaltybalance_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_astroname_lineedit", {"default_state": "disabled"}),
            ("change_state", "g_astroid_lineedit", {"default_state": "disabled"}),

            # Repeat Delivery
            ("push", "$storage.select_delivery_product", None),
            ("push", "$storage.repeat_order_task_id", None),
            ("change_state", "grp_repeat_delivery", {"default_state": "disabled"}),
            ("push", "$g_start_repeatdelivery_datetimeedit", "#date#beginning"),
            ("push", "$g_end_start_repeatdelivery_datetimeedit", "#date#ending + #timedelta(30)"),
            # ("if", "$storage.corp",
            #     "$g_startenddate_repeatdelivery_frame.show"),


            "$grp_phase_1_hidden_controls.hide",
            "$grp_manager_admin_tabs.hide",
            "$grp_customer_manager_admin_tabs.hide",
            "$grp_admin_controls.hide",
            "$grp_clerk_controls.hide",
            "$grp_mfg_media_controls.hide",
            "$g_selectgroupbox_label.hide",

            # Mfg
            # Media Fpe form
            ("push", "$storage.mfg_type", None),
            ("push", "$storage.mfg_filename", None),
            ("push", "$storage.mfg_media_id", None),


            ("push", "$storage.region_ids", []),
            ("push", "$storage.countery_ids", []),

            ("push", "$g_sales_table", []),

            ("push", "$g_sales_type_combbox", [{"name": "All",                    "value": "Sale, Layaway,Return from Customer"},
                                               {"name": "Sales",                  "value": "Sale"},
                                               {"name": "Returns from Customers", "value": "Return from Customer"},
                                               {"name": "Layaways",               "value": "Layaway"}]),

            ("push", "$g_sales_sumbyperiod_combbox", [{"name": "Events", "value": "events"},
                                                      {"name": "Month",  "value": "month"}]),

            ("push", "$g_sales_type_combbox.selected", "All"),
            ("push", "$g_sales_sumbyperiod_combbox.selected", "events"),

            ("push", "$g_sales_startdate_timeedit", "#date#beginning - #timedelta(90)"),
            ("push", "$g_sales_enddate_timeedit", "#date#ending"),

            # Employee Status
            ("push", "$storage.changed_payments", "#ordered_dict"),
            ("push", {"$g_employeestatus_combobox": [{"name": "Disable", "value": False},
                                                     {"name": "Enabled", "value": True}]}),

            # House Accounts
            ("change_state", "g_managehouseaccount_button", {"default_state": "disabled"}),
            ("change_state", "g_payhouseaccount_button", {"default_state": "disabled"}),
            # ("if", "$app.name == 'Cash Register' and $app.permissions.has_role(-9)",
            #     "$g_newhouseaccount_button.show",
            #     "$g_newhouseaccount_button.hide"),

            # Supplier Info
            ("push", "$storage.sup_loc_entity_id", None),
            ("push", "$storage.new_sup_loc_entity", False),
            ("push", "$storage.new_sup_loc_name", ""),
            ("push", "$storage.location_loc_ids", None),
            ("push", {"$g_supplierinfosendvia_combobox": [{"name": "Email", "value": "Email"},
                                                          {"name": "Print", "value": "Print"}]}),

            ("push", {"$g_verification_combobox": [{"name": "Required", "value": "Required"},
                                                   {"name": "Optional", "value": "Optional"},
                                                   {"name": "Never", "value": "Never"}]}),

            ("push", {"$g_supplierinfosendvia_combobox.selected": "Print"}),

            ("$g_manageentity_tabwidget.select_tab", "$g_contactinfo_widget#self"),
            ("change_state", "grp_brand_buttons", {"default_state": "disabled"}),

            "declare_entity_ids",

            ("push", "$storage.temp", [{"numt_contact_number_type": "Home", "phone_id": -2},
                                         {"numt_contact_number_type": "Mobile", "phone_id": -1},
                                         {"numt_contact_number_type": "Office", "phone_id": -4},
                                         {"numt_contact_number_type": "Fax", "phone_id": -5}]),

            ("push", "$storage.combobox", "#dict('numt_contact_number_type', #dict('match', 'numt_contact_number_type',"
                                          "'show', 'numt_contact_number_type',"
                                          "'data', $storage.temp))"),

            "$phone_droplist.set_combox_data($storage.combobox)",

            ("case", {
                "value": "$storage.status",

                "create": [
                    "$grp_all_hidden_controls.hide",
                    "$g_pidb_groupbox.hide",
                    "$grp_all_hidden_tabs.hide",
                    "$g_pagenav_frame.hide",
                    # "$g_selectgroupbox_label.show",
                    ("push", "$g_leadtime_lineedit", 3),
                    ("change_state", "grp_paperwork_breeder_buttons", {"default_state": "disabled"}),
                    ("change_state", "grp_paperwork_broker_buttons", {"default_state": "disabled"}),
                    ("change_state", "grp_paperwork_trans_buttons", {"default_state": "disabled"}),
                    ("change_state", "g_print_paperwork_broker_button", {"default_state": "disabled"}),
                    ("change_state", "g_print_paperwork_breeder_button", {"default_state": "disabled"}),
                    ("change_state", "g_print_paperwork_trans_button", {"default_state": "disabled"}),
                    ("change_state", "g_addpaperwork_breeder_button", {"default_state": "disabled"}),
                    ("change_state", "g_addpaperwork_broker_button", {"default_state": "disabled"}),
                    ("change_state", "g_addpaperwork_trans_button", {"default_state": "disabled"}),
                    ("push", {"$g_employeestatus_combobox.selected": True}),
                    "$phone_droplist.append_row",

                    ("if", "'supplier' in $form.input.flags",
                        [
                            ("push", "$g_orderfrequency_lineedit", "7"),
                            ("push", "$g_leadtime_lineedit", "3"),
                            ("push", "$g_minorderamount_lineedit", "0"),
                            # ("if", "$storage.corp != False and $storage.corp != None",
                            #     "$g_locations_supplier_frame.show")
                        ]),
                    ("if", "'customer' in $form.input.flags and $form.input.flags#length == 3",
                        [
                            '$email_droplist.append_row(#dict("eml_email", $form.input.flags[2], "eml_email_type_id", -1, "email_id", None, "emlt_email_type", "Home"))'
                        ],
                        "$email_droplist.append_row"
                     ),
                ],

                "update": [
                    "disable_buttons",
                    "$grp_all_hidden_controls.hide",
                    "$grp_all_hidden_tabs.hide",
                    "$g_selectgroupbox_label.hide",
                    ("push", "$paginator.total", "$form.input.ids#length"),
                    ("if", "$paginator.total < 2", "$g_pagenav_frame.hide"),
                    # ("if", "'supplier' in $form.input.flags",
                    #     ("if", "$storage.corp != False",
                    #         "$g_locations_supplier_frame.show")),
                ]
            }),

            ("if", "$app.permissions.has_role(-7,-17)",
                [
                    # ("if", "'create' not in $storage.status",
                    #     ("change_state", "grp_disablemanufacturer_cashier", {"default_state": "disabled"}),
                    "$g_managebrand_frame.hide"
                ]),

            # Subscriptions
            ("push", "$storage.entity_subscription_ids", []),

            # Weight
            ("push", "$storage.old_weight_unit", None),
            ("push", "$g_weightmeasure_combobox", [{"name": "lb", "value": "lb"},
                                                   {"name": "g", "value": "g"},
                                                   {"name": "kg", "value": "kg"}]),

            # Check if Sales Tab has been clicked
            ("push", "$storage.clicked_sales_tab", False)

            # Check if possible phone number and add on Create New Entity
            # ("push", "$storage.searchphonenumber", None),
            # ("if", "$form.input.qp_cnum_number != None",
            #     ("push", "$storage.searchphonenumber", "($form.input.qp_cnum_number if $form.input.qp_cnum_number#is_possible_number == True) or None")),
            # ("if", "$form.input.qp_smart_search != None",
            #      ("push", "$storage.searchphonenumber", "($form.input.qp_smart_search if $form.input.qp_smart_search#is_possible_number == True) or None")),
            # ("if", "$storage.searchphonenumber != None",
            #     [
            #         ("push", "$storage.phonenumber", {"cnum_number": "$storage.searchphonenumber[0]", "numt_contact_number_type": "Mobile",
            #                         "contact_number_id": None, "phone_id": PhoneNumberType.MOBILE}),
            #         "$phone_droplist.new_data($storage.phonenumber)",
            #     ])

        ],

        "set_permissions": [
            ("if", "$app.permissions.has_permission_other(1, -27) or "
                   "$app.permissions.has_permission_other(2, -27)",  # Edit Category/Sub Category
                [
                    ("if", "$app.name == 'Cash Register'",
                        "$g_taxes_groupbox.show"),

                    ("if", "$app.permissions.has_permission_other(2, -27)",  # Edit Category/Sub Category
                        "$g_updatetaxcertificates_label.show",
                        "$g_updatetaxcertificates_label.hide"),
                ],
                "$g_taxes_groupbox.hide"),
            ("if", "$app.permissions.has_permission_other(1, -33) == True",
                 "$g_updatebonusbucks_label.show",
                 "$g_updatebonusbucks_label.hide"),
            ("if", "$app.permissions.has_permission_other(1, -36) == True",
                "$g_frequentbuyerscontrols_frame.show",
                "$g_frequentbuyerscontrols_frame.hide"),
        ],

        "prepare_form_subtype": [
            ("$g_manageentity_tabwidget.select_tab", "$g_contactinfo_widget#self"),
            # "$g_pidb_groupbox.hide",

            {"case": {
                "value": "$form.input.flags",

                "employee": [
                    "$grp_employee_hidden_controls.hide",
                    "$grp_employee_hidden_tabs.hide",

                    "$grp_employee_controls.show",
                    ("if", "$app.name == 'Settings and Configuration'", "$grp_employee_tabs.show"),

                    ("push", "$g_main_label", "Manage Employee"),

                    "$form.set_width(950)",
                    "$form.set_height(615)"
                ],

                "customer": [
                    "$grp_customer_hidden_controls.hide",
                    "$grp_customer_hidden_tabs.hide",

                    "$grp_customer_controls.show",
                    "$grp_customer_tabs.show",

                    ("if", "$app.integrations.is_enabled(" + str(Integrations.LOYALTY) + ") == True",
                        "$g_loyalty_group.show"),

                    # hide all Astro controls at start
                    "$grp_astro_linked_controls.hide",
                    "$grp_astro_unlinked_controls.hide",

                    ("push", "$g_main_label", "Manage Customer")
                ],

                "supplier": [
                    "$grp_supplier_hidden_controls.hide",
                    "$grp_supplier_hidden_tabs.hide",

                    "$grp_supplier_controls.show",
                    "$grp_supplier_tabs.show",

                    ("push", "$g_main_label", "Manage Supplier")
                ],

                "manufacturer": [
                    "$grp_manufacturer_hidden_controls.hide",
                    "$grp_manufacturer_hidden_tabs.hide",

                    "$grp_manufacturer_controls.show",
                    "$grp_manufacturer_tabs.show",

                    ("push", "$g_main_label", "Manage Manufacturer")
                ],

                "breeder": [
                    "$grp_breeder_hidden_controls.hide",
                    "$grp_breeder_hidden_tabs.hide",

                    "$grp_breeder_controls.show",
                    "$grp_breeder_tabs.show",

                    ("push", "$g_main_label", "Manage Breeder, Broker, Transporter")
                ],

                "broker": [
                    "$grp_broker_hidden_controls.hide",
                    "$grp_broker_hidden_tabs.hide",

                    "$grp_broker_controls.show",
                    "$grp_broker_tabs.show",

                    ("push", "$g_main_label", "Manage Breeder, Broker, Transporter")
                ],

                "vet": [
                    "$grp_vet_hidden_controls.hide",
                    "$grp_vet_hidden_tabs.hide",

                    "$grp_vet_controls.show",
                    "$grp_vet_tabs.show",

                    ("push", "$g_main_label", "Manage Vet")
                ],

                "transporter": [
                    "$grp_transporter_hidden_controls.hide",
                    "$grp_transporter_hidden_tabs.hide",

                    "$grp_transporter_controls.show",
                    "$grp_transporter_tabs.show",

                    ("push", "$g_main_label", "Manage Breeder, Broker, Transporter")
                ]
            }},

            # .Set on_change events for comboboxes
            "$sales_datetime_range.set_on_change('load_product_sales')",
            "$g_sales_sumbyperiod_combbox.set_on_change('g_sales_sumbyperiod_combbox')",
            "$g_sales_type_combbox.set_on_change('g_sales_sumbyperiod_combbox')",
        ],

        "g_employeerole_combobox_on_change": [
            ("if", "$g_employeerole_combobox.selected.role_name == 'Admin'",
                ("change_state", "g_managepermission_button", {"default_state": "disabled"}),
                ("if", "$app.permissions.has_role(-9)",
                    ("change_state", "g_managepermission_button", {"default_state": "enabled"}),
                    ("change_state", "g_managepermission_button", {"default_state": "disabled"}),)),
            "manage_employeerole_options"
        ],

        "g_sales_sumbyperiod_combbox": ("if", "'Customer' in $c_group_checkcombobox.checked.role_name", "load_product_sales"),

        "load_data": {
            "transfers": {
                "names": [
                    "get_user_info",
                    "get_countries",
                    "get_email_types",
                    "get_all_locations",
                    "get_locations",
                    "get_iamge_ratio",
                ],

                "on_success": [
                    # ("push", "$g_emplocations_combobox", "$storage.location_id"),
                    "load_basic_data",
                ]
            }
        },

        "load_basic_data": [
            ("if", "'location' in $form.input.flags", ("transfer", "get_location_roles", "load_subtype_datasets")),
            ("if", "'location' not in $form.input.flags", ("transfer", "get_basic_roles", "load_subtype_datasets")),
        ],

        "load_subtype_datasets": {
            "transfers": {
                "names": [
                    "get_all_user_roles",
                    "get_all_apps",
                ],

                "on_success": [
                    ("if", "$storage.status[0] == 'update'",
                        [("if", "$form.input.ids != None",
                            [
                                ("push", "$storage.id", "$form.input.ids[$paginator.index]"),
                                ("push", "$storage.loc_entity_id", "$form.input.loc_entity_id[$paginator.index]")
                            ]),
                         ("transfer", "get_entity"),
                         ("transfer", "get_entity_api"),
                         ("push", "$g_country_combobox.selected", "$storage.country_id"),
                         ("push", "$g_state_combobox.selected", "$storage.region_id"),
                         ]),
                    ("if", "$storage.status[0] == 'create'",
                        [
                            ("if", "'employee' in $form.input.flags",
                                [
                                    ("push", "$g_locationaccess_list.selected", ["$storage.location_id"]),
                                    ("push", "$g_moduleaccess_list.selected", [8])
                                ]),  # 8 - "Time Clock"

                            ("if", "'supplier' in $form.input.flags",
                                [
                                    # ("if", "$form.input.corp == True and $storage.stats[0] != 'create'",
                                    #     "$g_locations_supplier_frame.show")
                                ]),

                            ("push", "$g_country_combobox.selected", "$storage.country_id"),
                            ("push", "$g_state_combobox.selected", "$storage.region_id")
                        ]),
                    ("push", "$storage.loading", False),
                ]
            }
        },

        "load_entity_datasets": [
            {"case": {
                "value":     "$c_group_checkcombobox.checked.role_name",

                "Breeder": [
                    # Astro tab hide/show
                    ("if", "$app.integrations.is_enabled(" + str(Integrations.PINOGY_WEBSITE) + ") == True",
                        [
                            "$g_breederslug_frame.show"
                        ],
                        [
                            "$g_breederslug_frame.hide"
                        ]),
                    ("if", "'Breeder' in $c_group_checkcombobox.checked.role_name",
                        [
                            ("if", "$ext.releases.config['customfields'] == 'false'",
                                "$g_manageentity_tabwidget.hide_tab('g_customfield_widget')",
                                [
                                    ("if", "$app.integrations.is_enabled(" + str(Integrations.ASTROLOYALTY) + ") == True",
                                        [
                                            "astro_enabled",

                                            "$g_manageentity_tabwidget.show_tab('g_customfield_widget')",
                                        ],
                                        "$g_manageentity_tabwidget.hide_tab('g_customfield_widget')")
                                ]),
                            ("transfers", ["read_breeder_paperworks", "get_custom_fields", "get_images"])
                        ])
                ],

                "Broker": [
                    # ("if", "'Broker' in $c_group_checkcombobox.checked.role_name",
                    #     [("transfers", ["read_broker_paperworks", "get_images"])])
                ],

                "Vet": [
                ],

                "Transporter": [
                    ("if", "'Transporter' in $c_group_checkcombobox.checked.role_name",
                        ("transfer", "read_trans_paperworks"))
                ],

                "Customer": [
                    ("transfer", "read_emergency_contact"),
                    ("if", "$app.integrations.is_enabled(" + str(Integrations.SAVEDPAYMENTS) + ") == True",
                        [
                            ("transfer", "get_show_saved_payments"),
                            "$g_manageentity_tabwidget.show_tab('g_savedcards_widget')",
                        ],
                        [
                            "$g_manageentity_tabwidget.hide_tab('g_savedcards_widget')",
                        ]),

                    ("if", "'Customer' in $c_group_checkcombobox.checked.role_name",
                        [
                            # Astro tab hide/show
                            ("if", "$app.integrations.is_enabled(" + str(Integrations.ASTROLOYALTY) + ") == True",
                             [
                                 "$g_manageentity_tabwidget.show_tab('g_astroinfo_widget')",
                                 "astro_enabled",
                                 ("if", "$storage.cust_attrs.astroloyalty.astro_customer_id",
                                    [
                                        ("transfer", "get_astro_status"),
                                    ],
                                    [
                                        "$grp_astro_linked_controls.hide",
                                        "$grp_astro_unlinked_controls.show"
                                    ]),
                             ],
                             [
                                 "$g_manageentity_tabwidget.hide_tab('g_astroinfo_widget')"
                             ]),
                            # DealsMedia tab hide/show
                            "maybe_show_petz_tab",
                            "load_saved_cards",
                        ]),

                    ("if", "$app.name == 'Cash Register'",
                        [
                            ("transfer", "get_website_integrations"),
                            "$g_salestab_tabWidget.show_tab('g_repeatdelivery_tab')",
                        ]),

                    # Show Saved Payment tab if Intigration is enabled
                    "$g_manageentity_tabwidget.show_tab('g_frequentbuyer_widget')",
                    ("transfers", ["get_taxexemptions", "get_houseaccounts",
                                   "get_buyer_program_status", "get_rewards_earned"]),


                    ("if", "$app.integrations.is_enabled(" + str(Integrations.LOYALTY) + ") == True",
                        [
                            "$g_loyaltycard_frame.show",
                            ("transfer", "get_bonusbucks"),
                        ],
                        [
                            "$g_loyaltycard_frame.hide"
                        ]),

                    # Show reset website password button if enabled
                    ("if", "$app.integrations.is_enabled(" + str(Integrations.PINOGY_WEBSITE) + ") == True",
                        "$g_manageentity_tabwidget.show_tab('g_website_tab')",
                        "$g_manageentity_tabwidget.hide_tab('g_website_tab')"),

                    ("transfer",  "get_customer_pets"),
                ],

                "Manufacturer": [
                    ("if", "'Manufacturer' in $c_group_checkcombobox.checked.role_name",
                        ("transfer", "get_brands", ("transfer", "get_mfg_iamge_ratio")))
                ],

                "Supplier": [
                    ("if", "$storage.corp == False",
                        [
                            "$g_corp_supplier_frame.hide",
                            "$g_supplier_frame.show",
                            ("transfer", "get_supplier_info"),
                        ],
                        [
                            "$g_corp_supplier_frame.show",
                            "$g_supplier_frame.hide",
                            ("transfer", "get_corp_supplier_info")
                        ])
                ]
            }},
            "$g_entityfirst_lineedit.set_focus"
        ],

        "load_brands": {
            "transfer": "get_brands"
        },

        "start_return": [
            ("if", "$app.name == 'Cash Register' and $g_sales_table.selected.txn_type != 'Return from Customer'",
             [
                 "$returninvoices_popup.return_invoices("
                 "$g_sales_table.selected.transaction_id#list, "
                 "$g_sales_table.selected.txn_type_number#list, "
                 "$g_sales_table.selected.txn_type#list, "
                 "$g_sales_table.selected.txn_ent_name#list, "
                 "$g_sales_table.selected.txn_closed_date#list, "
                 "$g_sales_table.selected.txn_grand_total#list)",

                 "$form.accept"
             ])
        ],

        # Sales Tab
        "load_product_sales": [
            ("push", "$g_sales_table", []),
            ("if", "$g_sales_sumbyperiod_combbox.selected.value == 'events'",
                [
                    ("change_state", "g_sales_table", {
                        "remote_sort": ["txn_closed_date", "txn_type", "txn_type_number", "user_display_name",
                                        "txnline_product_id", "txnline_product_description",
                                        "txnline_qty_sum", "txnline_amount", "txnline_discount",
                                        "net_price"],

                        "default_sort_field": "txn_closed_date desc,txn_type asc,txn_type_number asc",
                        "default_sort_order": "",

                        "bind": {
                            "columns": [
                                ("txn_closed_date", {"default": "Date"}),
                                ("txn_type", {"default": "Type"}),
                                ("txn_type_number", {"default": "Number"}),
                                ("user_display_name", {"default": "Employee"}),
                                ("txnline_product_id", {"default": "SKU"}),
                                ("txnline_product_description", {"default": "Description"}),
                                ("txnline_qty_sum", {"default": "Qty", "halign": "right"}),
                                ("txnline_amount", {"default": "Price", "halign": "right"}),
                                ("txnline_discount", {"default": "Discount", "halign": "right"}),
                                ("net_price", {"default": "Ext. Net Price", "halign": "right"}),
                                ("loc_report_code", {"default": "Location"})
                            ],

                            "show_fields": [
                                "$row.txn_closed_date#date",
                                "$row.txn_type",
                                ("$row.txn_type_number", {"on_link_click": ("transfer", "show_invoice")}),
                                "$row.user_display_name or ''",
                                "$row.txnline_product_id",
                                "$row.txnline_product_description or ''",
                                ("$row.txnline_qty_sum#quantity or 0", {"halign": "right"}),
                                ("$row.txnline_amount#currency or 0.00", {"halign": "right"}),
                                ("$row.txnline_discount#currency(2,0) or 0.00", {"halign": "right"}),
                                ("$row.net_price#currency or 0.00", {"halign": "right"}),
                                "$row.loc_report_code"
                            ]
                        },
                    }),
                    ("if", "$storage.id", ("transfer", "get_product_sales_by_event"))
                ],
                [

                    ("change_state", "g_sales_table", {
                         "remote_sort": ["period_date", "txn_type", "txnline_amount_sum",
                                         "net_price_sum", "txnline_discount_sum", "net_price_sum"],

                         "default_sort_field": "period_date",
                         "default_sort_order": "desc",

                         "bind": {
                             "columns": [
                                 ("period", {"default": "Period"}),
                                 ("txn_type", {"default": "Type"}),
                                 ("txnline_qty_sum", {"default": "Sum Qty", "halign": "right"}),
                                 ("txnline_amount_sum", {"default": "Sum Price", "halign": "right"}),
                                 ("txnline_discount_sum", {"default": "Sum Discount", "halign": "right"}),
                                 ("net_price_sum", {"default": "Sum Ext. Net Price", "halign": "right"})
                             ],

                             "show_fields": [
                                 "$row.period",
                                 "$row.txn_type",

                                 ("$row.txnline_qty_sum#int or 0", {"halign": "right"}),
                                 ("$row.txnline_amount_sum#currency or 0.00", {"halign": "right"}),

                                 ("$row.txnline_discount_sum#currency(2,0)", {"halign": "right"}),
                                 ("$row.net_price_sum#quantity", {"halign": "right"})
                             ]
                         },
                    }),
                    ("if", "$storage.id", ("transfer", "get_product_sales_summary"))
                ])
        ],

        "set_default_values": [
            ("if", "$app.name != 'Settings and Configuration'",
             ("push", "$storage.check_state", -10),
             ("push", "$storage.check_state", None)),

            {"case": {
                "value": "$form.input.flags",

                "employee": [
                    ("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Employee'}]),
                    "$c_group_checkcombobox.set_checked('Employee', True)",


                ],
                "supplier": [("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Supplier'}]),
                             "$c_group_checkcombobox.set_checked('Supplier', True)"],
                "manufacturer": [("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Manufacturer'}]),
                                 "$c_group_checkcombobox.set_checked('Manufacturer', True)"],
                "customer": [("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Customer'}]),
                             "$c_group_checkcombobox.set_checked('Customer', True)"],
                "breeder": [("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Breeder'}]),
                            "$c_group_checkcombobox.set_checked('Breeder', True)"],
                "broker": [("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Broker'}]),
                           "$c_group_checkcombobox.set_checked('Broker', True)"],
                "vet": [("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Vet'}]),
                        "$c_group_checkcombobox.set_checked('Vet', True)"],
                "transporter": [("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Transporter'}]),
                                "$c_group_checkcombobox.set_checked('Transporter', True)"],
            }},

            ("if", "$app.name != 'Settings and Configuration'",
                [
                    ("push", "$c_group_checkcombobox.disabled_items", [{'role_name': 'Employee'}]),


                    # ("if", "$storage.roles#where('role_id',-10).role_children_role_names[0]#length > 0",
                    #     [
                    #         ("push","$storage.id_list",("#map","$storage.roles#where('role_id',-10).role_children_role_names[0]","#dict('role_name',$_)")),
                    #         ("push", "$c_group_checkcombobox.disabled_items", "$storage.id_list"),
                    #     ])
                ]),
        ],

        "declare_entity_ids": {
            "push": {
                "$storage.home_phone_id": None,
                "$storage.mobile_phone_id": None,
                "$storage.office_phone_id": None,
                "$storage.fax_phone_id": None,

                "$storage.home_phone": None,
                "$storage.mobile_phone": None,
                "$storage.office_phone": None,
                "$storage.fax_phone": None,

                "$storage.address_id": None,
                "$storage.email_id": None,

                "$storage.tax_rule_id": None,

                "$storage.breeder_id": None,
                "$storage.broker_id": None,
                "$storage.vet_id": None,
                "$storage.transporter_id": None,

                "$storage.supplier_location_id": None
            }
        },

        "push_entity": {
            "case": {
                "value": "$storage.status",

                "update": [
                    ("if", "$result.ent_contact_numbers#length > 0",
                        [
                            ("call", addphonetypeid, {"numbers": "$result.ent_contact_numbers"},
                             ("if", "$result", ("push", "$storage.numbers", "$result"),
                              ("info_message", "Error in combining lists"))),
                            ("push", "$phone_droplist", "$storage.numbers")
                        ],
                        "$phone_droplist.clear_data"),

                    ("push", {
                        "$g_business_lineedit": "$result.ent_business_name",

                        "$storage.address_id": "$result.ent_addresses#any#get('address_id',None)",
                        "$g_addressline1_lineedit": "$result.ent_addresses#any#get('addr_address_1','')",
                        "$g_addressline2_lineedit": "$result.ent_addresses#any#get('addr_address_2','')",
                        "$g_city_lineedit": "$result.ent_addresses#any#get('addr_city','')",
                        "$g_zip_lineedit": "$result.ent_addresses#any#get('addr_postal_code','')",
                        "$g_country_combobox.selected": "$result.ent_addresses#any#get('addr_country_id','')",

                        "$email_droplist": "$result.ent_emails",

                        "$g_privatenotes_plaintextedit": "$result.ent_notes",
                        "$storage.entity_roles": "$result.ent_roles.erole_role_id"
                    }),

                    ("if", "-18 in $storage.entity_roles",
                        [
                            ("change_state", "g_group_checkcombobox", {"default_state": "disabled"}),
                            ("change_state", "g_managerole_button", {"default_state": "disabled"}),
                        ],
                        [
                            ("change_state", "g_group_checkcombobox", {"default_state": "enabled"}),
                            ("change_state", "g_managerole_button", {"default_state": "enabled"}),
                        ]),

                    ("if", "$result.ent_roles.erole_role_id",
                        "$c_group_checkcombobox.set_checked($storage.roles#where_list('role_id', $result.ent_roles.erole_role_id).role_name, True)"),

                    ("if", "$result.ent_emails#length == 0",
                        "$email_droplist.append_row"),

                    # That should be done after pushing to countries combobox,
                    # Dictionaries in python are unordered, that's why it's a separate `push` action
                    ("push", "$g_state_combobox.selected", "$result.ent_addresses#any#get('addr_region_id','')")
                ]
            }
        },

        "push_entity_subtype": [
            {"case": {
                "value": "$c_group_checkcombobox.checked.role_name",

                "Employee": [
                    ("if", "'from_entity' in $form.input.flags",
                        {"push": {
                            "$g_entityfirst_lineedit": "$result.ent_first_name  or ''",
                            "$g_entitylast_lineedit": "$result.ent_last_name or ''",

                            "$g_displayname_lineedit": "> {$result.ent_first_name or ''} {$result.ent_last_name or ''}",
                            "$g_employee_id_lineedit": "$result.ent_user.user_number or ''",

                            "$g_employeestatus_combobox.selected": "$result.ent_user.user_is_active",

                            "$g_locationaccess_list.selected": "$result.ent_user_locations.ualoc_loc_entity_id",
                            "$g_moduleaccess_list.selected": "$result.ent_user_apps.app_id",

                            "$g_privateemployee_plaintextedit": "$result.ent_user.user_notes or ''",

                            "$g_emplocations_combobox.selected": "$result.ent_user.user_default_loc_entity_id or $storage.location_id",
                        }},
                        [
                            {"push": {
                                "$g_entityfirst_lineedit": "$result.ent_first_name  or ''",
                                "$g_entitylast_lineedit": "$result.ent_last_name or ''",

                                "$g_displayname_lineedit": "$result.ent_user.user_display_name",
                                "$g_employee_id_lineedit": "$result.ent_user.user_number or ''",

                                "$g_employeerole_combobox.selected": "$result.ent_roles.erole_role_id#intersect($g_employeerole_combobox.data.role_id)[0]",
                                "$g_employeestatus_combobox.selected": "$result.ent_user.user_is_active",

                                "$g_locationaccess_list.selected": "$result.ent_user_locations.ualoc_loc_entity_id",
                                "$g_moduleaccess_list.selected": "$result.ent_user_apps.app_id",

                                "$g_privateemployee_plaintextedit": "$result.ent_user.user_default_loc_entity_id or $storage.location_id",

                                "$g_emplocations_combobox.selected": "$result.ent_user.user_default_loc_entity_id or $storage.location_id",
                            }},
                        ]),
                    ("if", "$app.name == 'Settings and Configuration'",
                        "$g_employeenotes_groupbox.show",
                        "$g_employeenotes_groupbox.hide"),
                 ],

                "Supplier": [
                    {"if": {
                        "condition": "$result.ent_supplier",
                        "then": {
                            "push": {
                                # Specific Location is already selected
                                "$storage.supplier_location_id": "$result.ent_supplier.supplier_location_id or ''",
                                "$g_accountnumber_lineedit": "$result.ent_supplier.suploc_account_number or ''",

                                "$g_leadtime_lineedit": "$result.ent_supplier.suploc_lead_time_days or '3'",
                                "$g_suppliernotes_plaintextedit": "$result.ent_supplier.suploc_notes or ''",
                                "$g_orderfrequency_lineedit": "$result.ent_supplier.suploc_order_frequency or '7'",

                                "$g_minorderamount_lineedit": "$result.ent_supplier.suploc_min_order_amount or '0'",
                                "$g_supplierinfosendvia_combobox.selected": "$result.ent_supplier.suploc_send_po_via or ''",
                                # "$g_verification_combobox.selected": "$result.ent_supplier.suploc_receive_verify or 'Optional'"
                            }
                        }
                    }}
                ],

                "Customer": [
                    {"push": {
                        "$g_entityfirst_lineedit": "$result.ent_first_name or ''",
                        "$g_entitylast_lineedit": "$result.ent_last_name or ''",
                        "$g_loyaltycard_lineedit": "$result.ent_customer.cust_loyalty_acct_code",
                        "$g_ent_birthday_timeedit": "($result.ent_customer.cust_birthday_date if $result.ent_customer.cust_birthday_date != None) or #datetime.strftime('%Y-%m-%d')",
                        "$g_warning_lineedit": "$result.ent_customer.cust_warning",
                        "$g_info_lineedit": "$result.ent_customer.cust_info",
                        "$storage.cust_attrs": "$result.ent_customer.cust_attrs",
                        "$g_webusername_lineedit": "$result.ent_user.user_username or ''",
                    }},
                    ("if", "$g_webusername_lineedit.data#length > 0",
                        [
                            "$g_createusername_button.hide",
                            "$g_resetpassword_button.show",
                        ],
                        [
                            "$g_createusername_button.show",
                            "$g_resetpassword_button.hide",
                        ]),
                    # Used for sending email invite for websites
                    # ("if", "$result.ent_user.user_username == None or $result.ent_user.user_username == ''",
                    #     [
                    #         "$g_resetpassword_frame.hide",
                    #         "$g_welcomeemail_button.show",
                    #     ],
                    #     [
                    #         "$g_resetpassword_frame.show",
                    #         "$g_welcomeemail_button.hide",
                    #     ])
                ],

                "Breeder": {
                    "push": {
                        "$g_entityfirst_lineedit": "$result.ent_first_name or ''",
                        "$g_entitylast_lineedit": "$result.ent_last_name or ''",
                        "$g_usda_lineedit": "$result.ent_pet_breeder.pbreeder_usda_num or ''",
                        "$g_state_lineedit": "$result.ent_pet_breeder.pbreeder_state_num or ''",
                        "$g_breederslug_lineedit": "$result.ent_pet_breeder.pbreeder_slug or ''"
                    }
                },

                "Broker": {
                    "push": {
                        "$g_entityfirst_lineedit": "$result.ent_first_name or ''",
                        "$g_entitylast_lineedit": "$result.ent_last_name or ''",
                        "$g_broker_usda_lineedit": "$result.ent_pet_broker.pbroker_usda_num or ''",
                        "$g_broker_state_lineedit": "$result.ent_pet_broker.pbroker_state_num or ''"
                    }
                },

                "Vet": {
                    "push": {
                        "$g_entityfirst_lineedit": "$result.ent_first_name or ''",
                        "$g_entitylast_lineedit": "$result.ent_last_name or ''",
                        "$g_vet_licence_lineedit": "$result.ent_pet_vet.pvet_licence or ''"
                    }
                },

                "Transporter": {
                    "push": {
                        "$g_entityfirst_lineedit": "$result.ent_first_name or ''",
                        "$g_entitylast_lineedit": "$result.ent_last_name or ''",
                        "$g_transporter_usda_lineedit": "$result.ent_pet_transporter.ptrnsp_usda_num or ''",
                        "$g_transporter_state_lineedit": "$result.ent_pet_transporter.ptrnsp_state_num or ''"

                    }
                },

                "Manufacturer": {
                    "push": {
                        "$g_featured_checkbox": "$result.ent_manufacturer.mfgr_is_featured",
                        "$g_public_checkbox": "$result.ent_manufacturer.mfgr_is_public",
                        "$g_mfg_marketing_description_lineedit": "$result.ent_manufacturer.mfgr_marketing_header or ''",
                        "$g_mfg_marketing_emessage_plaintextedit": "$result.ent_manufacturer.mfgr_marketing_description or ''"
                    }
                }
            }},
        ],

        "pull_entity_ids": {
            "pull": {
                "qp_entity_id": "$storage.id",
                "qp_usable_entity_id": "$storage.id",

                "_qp_tax_rule_id": "$storage.tax_rule_id or None",
            }
        },

        "pull_entity": [
            ("call", checkphonetypeid, {"ids": "$phone_droplist.data.numt_contact_number_type",
                                        "numbers": "$phone_droplist.data.cnum_number",
                                        "number_id": "$phone_droplist.data.contact_number_id"},
             ("if", "$result", ("push", "$storage.new_numbers", "$result"),
              ("info_message", "Error in combining lists"))),

            ("call", checkemailtypeid, {"emails": "$email_droplist.data.eml_email",
                                        "email_type_id": "$email_droplist.data.eml_email_type_id",
                                        "email_id": "$email_droplist.data.email_id"},
             ("if", "$result", ("push", "$storage.new_emails", "$result"),
              ("info_message", "Error in combining lists"))),

            ("if", "-18 not in $storage.entity_roles", # Check if entity is warehouse
                ("if", "-24 in $storage.entity_roles", # Check if Web Customer
                    {"pull": {"qp_erole_role_id": "$c_group_checkcombobox.checked.role_id#add_to_list(-24)"}},
                    {"pull": {"qp_erole_role_id": "$c_group_checkcombobox.checked.role_id"}}),
                {"pull": {"qp_erole_role_id": "$storage.entity_roles"}}
             ),

            {"pull": {
                "_qp_ent_first_name": "$g_entityfirst_lineedit",
                "_qp_ent_last_name": "$g_entitylast_lineedit",

                "qp_ent_business_name": "$g_business_lineedit",

                # "qp_erole_role_id": "($c_group_checkcombobox.checked.role_id#add_to_list(-24) if -24 in "
                #                     "$storage.entity_roles) or $c_group_checkcombobox.checked.role_id",

                "qp_cnum_contact_number_type_id": "$storage.new_numbers[0]",
                "qp_cnum_primary": "$storage.new_numbers[3]",
                "qp_contact_number_id": "$storage.new_numbers[2]",
                "_qp_cnum_number": "$storage.new_numbers[1] if $storage.new_numbers[1] != ','",

                "qp_eml_email": "$storage.new_emails[1]",
                "qp_eml_primary": "$storage.new_emails[3]",
                "qp_eml_email_type_id": "$storage.new_emails[0]",
                "qp_email_id": "$storage.new_emails[2]",

                "qp_ent_notes": "$g_privatenotes_plaintextedit",
                "qp_delete_missing_postal_addresses": False,
            }},

            ("if", "$g_city_lineedit or $g_zip_lineedit",
                {"pull": {
                    "qp_address_id": "$storage.address_id or ('@null' if $g_addressline1_lineedit)",
                    "_qp_addr_rank": "-2",

                    "qp_addr_city": "$g_city_lineedit or None",
                    "qp_addr_address_type_id": "-1",
                    "qp_addr_region_id": "$g_state_combobox.selected.postal_region_id",
                    "qp_addr_postal_code": "$g_zip_lineedit or None",
                    "qp_addr_country_id": "$g_country_combobox.selected.country_id",

                    "qp_addr_address_1": "$g_addressline1_lineedit",
                    "qp_addr_address_2": "($g_addressline2_lineedit if $g_addressline2_lineedit) or ''",
                }},
                {"pull": {
                    "qp_address_id": ""
                }})
        ],

        "pull_entity_subtype": [
            {"case": {
                "value": "$c_group_checkcombobox.checked.role_name",

                "Employee": [
                    ("if", "-24 in $storage.entity_roles",
                     [
                        ("push", "$storage.role", -24),
                        {"pull": {"qp_erole_role_id": "$c_group_checkcombobox.checked.role_id +  $storage.role#list + $g_employeerole_combobox.selected.role_id#list"}},
                    ],
                        {"pull": {"qp_erole_role_id": "$c_group_checkcombobox.checked.role_id + $g_employeerole_combobox.selected.role_id#list"}}),

                    {"pull": {
                        "_qp_addr_address_type": "(('-1' if $g_addressline1_lineedit) or None)",

                        "qp_user_display_name": "$g_displayname_lineedit",
                        "qp_user_username": "$g_displayname_lineedit",
                        "qp_user_number": "$g_employee_id_lineedit or ''",

                        "_qp_user_password": "$g_password_lineedit if ($g_password_lineedit)",

                        "qp_user_is_active": "$g_employeestatus_combobox.selected.value",

                        "qp_user_notes": "$g_privateemployee_plaintextedit.data",

                        "_qp_ent_accessible_apps": "$g_moduleaccess_list.selected.app_id",
                        "_qp_ent_accessible_locations": "$g_locationaccess_list.selected.loc_entity_id",
                        "qp_user_default_loc_entity_id": "$g_emplocations_combobox.selected.loc_entity_id"
                    }
                }],

                "Manufacturer": {
                    "pull": {
                        "_qp_mfg_brand_id": "$g_brands_table.data.mfg_brand_id",
                        "_qp_mfgbr_brand_name": "$g_brands_table.data.mfgbr_brand_name",
                        "qp_mfgr_is_featured": "$g_featured_checkbox.data",
                        "qp_mfgr_marketing_header": "$g_mfg_marketing_description_lineedit",
                        "qp_mfgr_marketing_description": "$g_mfg_marketing_emessage_plaintextedit.data",
                        "qp_mfgr_is_public": "$g_public_checkbox.data"
                    }
                },

                "Breeder": {
                    "pull": {
                        "qp_pbreeder_usda_num": "$g_usda_lineedit",
                        "qp_pbreeder_state_num": "$g_state_lineedit",
                        "qp_pbreeder_slug": "$g_breederslug_lineedit"
                    }
                },

                "Broker": {
                    "pull": {
                        "qp_pbroker_usda_num": "$g_broker_usda_lineedit",
                        "qp_pbroker_state_num": "$g_broker_state_lineedit",
                    }
                },

                "Vet": {
                    "pull": {
                        "qp_pvet_licence": "$g_vet_licence_lineedit"
                    }
                },

                "Transporter": {
                    "pull": {
                        "qp_ptrnsp_usda_num": "$g_transporter_usda_lineedit",
                        "qp_ptrnsp_state_num": "$g_transporter_state_lineedit",
                    }
                },

                "Supplier": [
                    ("if", "'create' in $form.input.flags",
                        {"pull": {
                            "qp_supplier_location_id": "$storage.supplier_location_id or None",
                            "qp_suploc_loc_entity_id": "$storage.location_id",
                            "qp_suploc_sup_entity_id": "$storage.id",

                            "qp_suploc_account_number": "$g_accountnumber_lineedit or ''",
                            "qp_suploc_lead_time_days": "$g_leadtime_lineedit",
                            "qp_suploc_order_frequency": "$g_orderfrequency_lineedit",
                            "qp_suploc_notes": "$g_suppliernotes_plaintextedit or None",

                            "_qp_suploc_min_order_amount": "$g_minorderamount_lineedit",
                            "qp_suploc_send_po_via": "$g_supplierinfosendvia_combobox.selected.value",
                            "qp_suploc_receive_verify": "$g_verification_combobox.selected.value or 'Optional'"
                        }})
                ],

                "Customer": [
                    {
                    "pull": {
                        "qp_cust_loyalty_acct_code": "$g_loyaltycard_lineedit",
                        "qp_cust_warning": "$g_warning_lineedit",
                        "qp_cust_info": "$g_info_lineedit",
                        "_qp_cust_birthday_date": "($g_ent_birthday_timeedit.ymd if $g_ent_birthday_timeedit.ymd != #datetime.strftime('%Y-%m-%d')) or None",
                    }
                }]
            }},
        ],

        "prepare_create_disclaimer_names": [
            # Disclaimer name, value pairs that differs from default ones
            ("if", "$g_disclaimer_layaway_plaintextedit == ''", ("push", "$g_disclaimer_layaway_plaintextedit", " ")),
            ("if", "$g_disclaimer_sale_plaintextedit == ''", ("push", "$g_disclaimer_sale_plaintextedit", " ")),
            ("if", "$g_disclaimer_quote_plaintextedit == ''", ("push", "$g_disclaimer_quote_plaintextedit", " ")),
            ("if", "$g_disclaimer_return_plaintextedit == ''", ("push", "$g_disclaimer_return_plaintextedit", " ")),

            ("push", "$storage.receipt_create_disclaimer_names", [
                "_'layaway_receipt_disclaimer' if (-5 in $storage.receipt_disclaimer_ids and $g_disclaimer_layaway_plaintextedit != $storage.default_disclaimers#find_where('stn_name', 'layaway_receipt_disclaimer')#get('stn_value'))",
                "_'sale_receipt_disclaimer' if (-9 in $storage.receipt_disclaimer_ids and $g_disclaimer_sale_plaintextedit != $storage.default_disclaimers#find_where('stn_name', 'sale_receipt_disclaimer')#get('stn_value'))",
                "_'quote_receipt_disclaimer' if (-10 in $storage.receipt_disclaimer_ids and $g_disclaimer_quote_plaintextedit != $storage.default_disclaimers#find_where('stn_name', 'quote_receipt_disclaimer')#get('stn_value'))",
                "_'return_from_customer_receipt_disclaimer' if (-11 in $storage.receipt_disclaimer_ids and $g_disclaimer_return_plaintextedit != $storage.default_disclaimers#find_where('stn_name', 'return_from_customer_receipt_disclaimer')#get('stn_value'))",
            ]),
            ("push", "$storage.receipt_create_disclaimer_values", [
                "_$g_disclaimer_layaway_plaintextedit.data.replace(',', '@~') if (-5 in $storage.receipt_disclaimer_ids and $g_disclaimer_layaway_plaintextedit != $storage.default_disclaimers#find_where('stn_name', 'layaway_receipt_disclaimer')#get('stn_value'))",
                "_$g_disclaimer_sale_plaintextedit.data.replace(',', '@~') if (-9 in $storage.receipt_disclaimer_ids and $g_disclaimer_sale_plaintextedit != $storage.default_disclaimers#find_where('stn_name', 'sale_receipt_disclaimer')#get('stn_value'))",
                "_$g_disclaimer_quote_plaintextedit.data.replace(',', '@~') if (-10 in $storage.receipt_disclaimer_ids and $g_disclaimer_quote_plaintextedit != $storage.default_disclaimers#find_where('stn_name', 'quote_receipt_disclaimer')#get('stn_value'))",
                "_$g_disclaimer_return_plaintextedit.data.replace(',', '@~') if (-11 in $storage.receipt_disclaimer_ids and $g_disclaimer_return_plaintextedit != $storage.default_disclaimers#find_where('stn_name', 'return_from_customer_receipt_disclaimer')#get('stn_value'))"
            ])
        ],

        "prepare_update_disclaimer_ids": [
            # Collect edited disclaimers
            ("if", "$g_disclaimer_layaway_plaintextedit == ''", ("push", "$g_disclaimer_layaway_plaintextedit", " ")),
            ("if", "$g_disclaimer_sale_plaintextedit == ''", ("push", "$g_disclaimer_sale_plaintextedit", " ")),
            ("if", "$g_disclaimer_quote_plaintextedit == ''", ("push", "$g_disclaimer_quote_plaintextedit", " ")),
            ("if", "$g_disclaimer_return_plaintextedit == ''", ("push", "$g_disclaimer_return_plaintextedit", " ")),

            ("push", "$storage.receipt_update_disclaimer_ids", [
                "_$storage.location_disclaimers#find_where('stn_name', 'layaway_receipt_disclaimer')#get('setting_id') if (-5 not in $storage.receipt_disclaimer_ids and $g_disclaimer_layaway_plaintextedit != $storage.location_disclaimers#find_where('stn_name', 'layaway_receipt_disclaimer')#get('stn_value'))",
                "_$storage.location_disclaimers#find_where('stn_name', 'sale_receipt_disclaimer')#get('setting_id') if (-9 not in $storage.receipt_disclaimer_ids and $g_disclaimer_sale_plaintextedit != $storage.location_disclaimers#find_where('stn_name', 'sale_receipt_disclaimer')#get('stn_value'))",
                "_$storage.location_disclaimers#find_where('stn_name', 'quote_receipt_disclaimer')#get('setting_id') if (-10 not in $storage.receipt_disclaimer_ids and $g_disclaimer_quote_plaintextedit != $storage.location_disclaimers#find_where('stn_name', 'quote_receipt_disclaimer')#get('stn_value'))",
                "_$storage.location_disclaimers#find_where('stn_name', 'return_from_customer_receipt_disclaimer')#get('setting_id') if (-11 not in $storage.receipt_disclaimer_ids and $g_disclaimer_return_plaintextedit != $storage.location_disclaimers#find_where('stn_name', 'return_from_customer_receipt_disclaimer')#get('stn_value'))",
            ]),
            ("push", "$storage.receipt_update_disclaimer_values", [
                "_$g_disclaimer_layaway_plaintextedit.data.replace(',', '@~') if (-5 not in $storage.receipt_disclaimer_ids and $g_disclaimer_layaway_plaintextedit != $storage.location_disclaimers#find_where('stn_name', 'layaway_receipt_disclaimer')#get('stn_value'))",
                "_$g_disclaimer_sale_plaintextedit.data.replace(',', '@~') if (-9 not in $storage.receipt_disclaimer_ids and $g_disclaimer_sale_plaintextedit != $storage.location_disclaimers#find_where('stn_name', 'sale_receipt_disclaimer')#get('stn_value'))",
                "_$g_disclaimer_quote_plaintextedit.data.replace(',', '@~') if (-10 not in $storage.receipt_disclaimer_ids and $g_disclaimer_quote_plaintextedit != $storage.location_disclaimers#find_where('stn_name', 'quote_receipt_disclaimer')#get('stn_value'))",
                "_$g_disclaimer_return_plaintextedit.data.replace(',', '@~') if (-11 not in $storage.receipt_disclaimer_ids and $g_disclaimer_return_plaintextedit != $storage.location_disclaimers#find_where('stn_name', 'return_from_customer_receipt_disclaimer')#get('stn_value'))"
            ])
        ],

        "save_entity": [
            "validate_entity_employee"
        ],

        "cancel_entity": [
            "$form.accept"
        ],

        "validate_entity_employee": {
            "if": {
                "condition": "'Employee' in $c_group_checkcombobox.checked.role_name and $app.name == 'Settings and Configuration'",

                "then": [
                    ("case", {
                        "value": "$storage.status",

                        "create": [
                            {"validate": {
                                "group_name": "employee_validations_create",

                                "on_success": [
                                    "validate_entity_supplier"
                                ],
                                # "on_failure": "show_error"
                            }}
                        ],

                        "update": [
                            {"validate": {
                                "group_name": "employee_validations",

                                "on_success": [
                                    "validate_entity_supplier"
                                ],
                                # "on_failure": "show_error"
                            }}
                        ]
                    }),


                ],

                "else": [
                    "validate_entity_supplier"
                ]
            }
        },

        "validate_entity_supplier": [
            ("if", "$storage.corp == False",

                {"if": {
                    "condition": "'Supplier' in $c_group_checkcombobox.checked.role_name",

                    "then": [
                        {"validate": {
                            "group_name": "supplier_validations_non_corp",
                            "on_success": "validate_entity_manufacturer",

                            "on_failure": "show_error"
                        }},
                    ],
                    "else": [
                        "validate_entity_manufacturer"
                    ]
                }},
                {"if": {
                    "condition": "'Supplier' in $c_group_checkcombobox.checked.role_name",

                    "then": [
                        {"validate": {
                            "group_name": "supplier_validations",
                            "on_success": "validate_entity_manufacturer",

                            "on_failure": "show_error"
                        }},
                    ],

                    "else": [
                        "validate_entity_manufacturer"
                    ]
             }})
        ],

        "validate_entity_manufacturer": {
            "if": {
                "condition": "'Manufacturer' in $c_group_checkcombobox.checked.role_name",

                "then": {
                    "validate": {
                        "group_name": "manufacturer_validations",
                        "on_success": "validate_entity_breeder",

                        "on_failure": "show_error"
                    }
                },

                "else": [
                    "validate_entity_breeder"
                ]
            }
        },

        "validate_entity_breeder": {
            "if": {
                "condition": "'Breeder' in $c_group_checkcombobox.checked.role_name",

                "then": {
                    "validate": {
                        "group_name": "breeder_validations",
                        "on_success": "validate_entity_broker",

                        "on_failure": "show_error"
                    }
                },

                "else": [
                    "validate_entity_broker"
                ]
            }
        },

        "validate_entity_broker": {
            "if": {
                "condition": "'Broker' in $c_group_checkcombobox.checked.role_name",

                "then": {
                    "validate": {
                        "group_name": "broker_validations",
                        "on_success": "validate_entity_vet",

                        "on_failure": "show_error"
                    }
                },

                "else": [
                    "validate_entity_vet"
                ]
            }
        },

        "validate_entity_vet": {
            "if": {
                "condition": "'Vet' in $c_group_checkcombobox.checked.role_name",

                "then": {
                    "validate": {
                        "group_name": "vet_validations",
                        "on_success": "validate_entity_transporter",

                        "on_failure": "show_error"
                    }
                },

                "else": [
                    "validate_entity_transporter"
                ]
            }
        },

        "validate_entity_transporter": {
            "if": {
                "condition": "'Transporter' in $c_group_checkcombobox.checked.role_name",

                "then": {
                    "validate": {
                        "group_name": "transporter_validations",
                        "on_success": "validate_entity_customer",

                        "on_failure": "show_error"
                    }
                },

                "else": [
                    "validate_entity_customer"
                ]
            }
        },

        "validate_entity_customer": {
            "if": {
                "condition": "'Customer' in $c_group_checkcombobox.checked.role_name",

                "then": {
                    "validate": {
                        "group_name": "customer_validations",
                        "on_success": "validate_phone",
                    }
                },

                "else": [
                    "validate_phone"
                ]
            }
        },

        "validate_phone": {
            "validate": {
                "group_name": "phone_validations",
                "on_success": "validate_address_email",
                "on_failure": ("error_message", "'\n'.join($errors)")
            }
        },

        "validate_address_email": {
            "if": {
                "condition": "$g_addressline1_lineedit or $g_city_lineedit or $g_zip_lineedit",
                "then": [
                    ("if", "$g_country_combobox.selected.country_id == 231",  # USA
                        {
                            "validate": {
                                "group_name": "address_us_validations",
                                "on_success": "validate_entity_email",
                                "on_failure": "show_error"
                            }
                        },
                        ("if", "$g_country_combobox.selected.country_id == 38",  # Canada
                             {
                                 "validate": {
                                     "group_name": "address_can_validations",
                                     "on_success": "validate_entity_email",
                                     "on_failure": "show_error"
                                 }
                             },
                             ("if", "$g_country_combobox.selected.country_id == 142",  # Mexico
                                 {
                                     "validate": {
                                         "group_name": "address_mex_validations",
                                         "on_success": "validate_entity_email",
                                         "on_failure": "show_error"
                                     }
                                 },
                                 {
                                     "validate": {
                                          "group_name": "address_other_validations",
                                          "on_success": "validate_entity_email",
                                          "on_failure": "show_error"
                                      }
                                 },

                              ))),

                ],
                "else": [
                    "validate_entity_email"
                ]
            }
        },

        "validate_entity_email": {
            "validate": {
                "group_name": "email_validations",
                "on_success": "save_entity_changes",
                "on_failure": "show_error"
            }
        },

        "save_entity_changes": {
            "case": {
                "value": "$storage.status",

                "create": [
                    ("if", "'employee' in $form.input.flags",
                        ("if", "$g_employeerole_combobox.selected.role_name != 'Lite User'",
                            ("transfer", "get_employee_check"),
                            ("transfer", "get_employee_lite_check")),
                        [
                            ("transfer", "save_new_entity",
                                ("if", "$storage.close",
                                    [
                                        ("if", "$storage.close == 'AddAddress'",
                                             [
                                                 ("push", "$storage.close", "True"),
                                                 ("push", "$storage.status[0]", "update"),
                                                 "load_data",
                                                 ("transfer", "add_entity_address"),
                                             ],
                                             ("if", "$storage.close == 'Taxes'",
                                                [
                                                    ("transfer", "update_taxcertificates"),
                                                    ("push", "$storage.close", "True"),
                                                    ("push", "$storage.status[0]", "update"),
                                                    "load_data",
                                                ],
                                                ("if", "$storage.close == 'AddPet'",
                                                    [
                                                        ("push", "$storage.close", "True"),
                                                        ("push", "$storage.status[0]", "update"),
                                                        "load_data",
                                                        ("transfer", "create_customer_pet"),
                                                    ],
                                                    ("if", "$storage.close == 'AddContact'",
                                                        [
                                                            ("push", "$storage.close", "True"),
                                                            ("push", "$storage.status[0]", "update"),
                                                            "load_data",
                                                            ("transfer", "create_emergency_contact"),
                                                        ],
                                                            "$form.close")))),
                                    ],
                                    [
                                        ("push", "$storage.close", "True"),
                                        ("push", "$storage.status[0]", "update"), "load_data"
                                    ])
                            )
                        ]),
                ],
                "update": [
                    ("if", "'from_entity' in $form.input.flags",
                        ("if", "$g_employeerole_combobox.selected.role_name != 'Lite User'",
                            ("transfer", "get_employee_check"),
                            ("transfer", "get_employee_lite_check")),
                        "save_new_entity_2")
                ]
            }
        },

        "save_new_entity_2": [
            ("if", "'location' in $form.input.flags", ("transfer", "update_timezone")),
            # ("if", "$storage.use_pidb", ("transfer", "update_get_pidb")),
            ("if", "'create' not in $form.input.flags",
                ("if", "'Supplier' in $c_group_checkcombobox.checked.role_name",
                    [
                        ("if", "$storage.supplier_location_id == '' and $storage.loading == False",
                            [
                                ("if", "$storage.corp == False",
                                    ("transfer", "create_current_multi_loc_supplier"))
                            ],
                            [
                                ("if", "$storage.corp == False",
                                    ("transfer", "update_current_entity_loc_supplier"))
                            ]),
                    ])),

            ("if", "'create_loc' not in $form.input.flags",
                ("transfer", "save_existing_entity",
                    ("if", "$storage.close", "flip_if_needed", [("push", "$storage.close", "True"), "load_data"])),
                [
                    ("if", "$storage.sup_loc_entity_id == ''",
                        ("transfer", "create_current_multi_loc_supplier", ("transfer", "save_existing_entity",
                                ("if", "$storage.close", "flip_if_needed", [("push", "$storage.close", "True"), "load_data"]))),
                        ("transfer", "update_current_entity_loc_supplier",
                            ("transfer", "save_existing_entity",
                                ("if", "$storage.close", "flip_if_needed", [("push", "$storage.close", "True"), "load_data"]))))
                ])
        ],

        "flip_if_needed": [
            ("if", "$paginator.is_last", "$form.accept"),
            ("if", "not $paginator.is_last", "$paginator.next")
        ],

        "hide_role_controls": [
            "$grp_all_hidden_controls.hide",
            "$grp_all_hidden_tabs.hide",

            ("case_in", {
                "collection": "$c_group_checkcombobox.checked.role_name",

                "Customer": [
                    "$grp_customer_hidden_controls.hide",
                ],

                "Manufacturer": [
                    "$grp_manufacturer_hidden_controls.hide",
                ],

                "Supplier": [
                    "$grp_supplier_hidden_controls.hide",
                ],

                "Breeder": [
                    "$grp_breeder_hidden_controls.hide",
                ],

                "Broker": [
                    "$grp_broker_hidden_controls.hide",
                ],

                "Vet": [
                    "$grp_vet_hidden_controls.hide",
                ],

                "Transporter": [
                    "$grp_transporter_hidden_controls.hide",
                ],

                "Employee": [
                    "$grp_employee_hidden_controls.hide",
                ]
            })
        ],

        "show_role_controls": [
            ("case_in", {
                "collection": "$c_group_checkcombobox.checked.role_name",

                "Customer": [
                    "$grp_customer_controls.show",
                    "$grp_customer_tabs.show"
                ],

                "Manufacturer": [
                    "$grp_manufacturer_controls.show",
                    "$grp_manufacturer_tabs.show"
                ],

                "Supplier": [
                    "$grp_supplier_controls.show",
                    "$grp_supplier_tabs.show"
                ],

                "Breeder": [
                    "$grp_breeder_controls.show",
                    "$grp_breeder_tabs.show"
                ],

                "Broker": [
                    "$grp_broker_controls.show",
                    "$grp_broker_tabs.show"
                ],

                "Vet": [
                    "$grp_vet_controls.show",
                    "$grp_vet_tabs.show"
                ],

                "Transporter": [
                    "$grp_transporter_controls.show",
                    "$grp_transporter_tabs.show"
                ],

                "Employee": [
                    "$grp_employee_controls.show",
                    ("if", "$app.name == 'Settings and Configuration'", "$grp_employee_tabs.show"),
                ]
            }),
            "enable_buttons",
            ("if", "-18 in $storage.entity_roles",
                 ("change_state", "g_managerole_button", {"default_state": "disabled"}),
                 ("change_state", "g_managerole_button", {"default_state": "enabled"})),

        ],

        "manage_role_controls": [
            "$grp_all_hidden_controls.hide",
            "$grp_all_hidden_tabs.hide",

            ("case_in", {
                "collection": "$c_group_checkcombobox.checked.role_name",

                "Customer": [
                    "$g_selectgroupbox_label.hide",
                    "$grp_customer_controls.show",
                    "$grp_customer_tabs.show"
                ],

                "Manufacturer": [
                    "$grp_manufacturer_hidden_controls.hide",
                    "$g_selectgroupbox_label.hide",
                    "$grp_manufacturer_controls.show",
                    "$grp_manufacturer_tabs.show"
                ],

                "Supplier": [
                    "$g_selectgroupbox_label.hide",
                    "$grp_supplier_controls.show",
                    "$grp_supplier_tabs.show"
                ],

                "Breeder": [
                    "$g_selectgroupbox_label.hide",
                    "$grp_breeder_controls.show",
                    "$grp_breeder_tabs.show"
                ],

                "Broker": [
                    "$g_selectgroupbox_label.hide",
                    "$grp_broker_controls.show",
                    "$grp_broker_tabs.show"
                ],

                "Vet": [
                    "$g_selectgroupbox_label.hide",
                    "$grp_vet_controls.show",
                    "$grp_vet_tabs.show"
                ],

                "Transporter": [
                    "$g_selectgroupbox_label.hide",
                    "$grp_transporter_controls.show",
                    "$grp_transporter_tabs.show"
                ],

                "Employee": [
                    "$g_selectgroupbox_label.hide",
                    "$grp_employee_controls.show",
                    ("if", "$app.name == 'Settings and Configuration'", "$grp_employee_tabs.show"),
                ]
            }),
            # ("if", "not $c_group_checkcombobox.checked.role_name",
            #     "$g_selectgroupbox_label.show",
            #     "$g_selectgroupbox_label.hide")

        ],

        "manage_employeerole_options": [
            {"if": {
                "condition": "$storage.status[0] == 'create'",
                "then": [
                    {"if": {
                        "condition": "($g_employeerole_combobox.selected.role_name == 'Clerk')",
                        "then": [
                            ("push", "$g_moduleaccess_list.selected", [4, 8])],
                        "else": [
                            ("if", "($g_employeerole_combobox.selected.role_name == 'Admin') or " +
                                             "($g_employeerole_combobox.selected.role_name == 'Manager')",
                                ("push", "$g_moduleaccess_list.selected", "$g_moduleaccess_list.data.app_id")),
                            ("if", "$g_employeerole_combobox.selected.role_name == 'Cashier'",
                             ("push", "$g_moduleaccess_list.selected", [4])),
                        ]
                    }},
                ],
                "else": [
                    ("if", "($g_employeerole_combobox.selected.role_name == 'Clerk')",
                        ("if", "($g_moduleaccess_list.selected)",
                            ("push", "$g_moduleaccess_list.selected", "$g_moduleaccess_list.selected.app_id#unite(#list(8))"),
                            ("push", "$g_moduleaccess_list.selected", [8]))),
                ],
            }},
            ("if", "$g_employeerole_combobox.selected.role_name == 'Lite User'",
                [
                    "$g_moduleaccess_list.uncheck_all",
                    ("push", "$g_moduleaccess_list.selected", [8]),
                    ("change_state", "g_moduleaccess_list", {"default_state": "disabled"}),
                ],
                [
                    ("change_state", "g_moduleaccess_list", {"default_state": "enabled"})
                ]),
            {"if": {
                "condition": "($g_employeerole_combobox.selected.role_name == 'Cashier')",
                "then": [
                    ("push", "$g_moduleaccess_list.selected", [4, 8])],
            }},
            {"if": {
                "condition": "($g_employeerole_combobox.selected.role_name == 'Assistant Manager')",
                "then": [
                    ("push", "$g_moduleaccess_list.selected", [3, 4, 5, 6, 8, 9, 11])],
            }},
        ],

        "add_brand_to_table": [
            ("$g_brands_table.append_row", {"mfg_brand_id": None,
                                            "mfgbr_brand_name": "$storage.brand_name"})
        ],

        "update_brand_in_table": [
            ("$g_brands_table.update_rows($g_brands_table.all_selected, $storage.brand_names)")
        ],

        "delete_brand_from_table": [
            ("if", "$g_brands_table.all_selected#reject('mfg_brand_id', None)#length > 0",
                ("transfer", "delete_brand"),
                ("$g_brands_table.remove_rows", "$g_brands_table.all_selected"))
        ],

        "show_admin_tabsncontrols": [
            ("if", "$app.permissions.has_role(-9,-11)", "$grp_admin_controls.show"),           # Admin

            {"case": {
                "value": "$form.input.flags",

                "location": [
                    ("if", "$app.permissions.has_role(-8,-19)", "$grp_location_manager_admin_tabs.show"),       # Manager
                    ("if", "$app.permissions.has_role(-9,-11)", "$grp_location_manager_admin_tabs.show")        # Admin
                ],

                "employee": [
                ],

                "customer": [
                ],

                "supplier": [
                ],

                "manufacturer": [
                ],

                "breeder": [
                ],

                "broker": [
                ],

                "vet": [
                ],

                "transporter": [
                ]
            }}
        ],

        "load_breeder_paperworks": [
            ("transfer", {
                "name": "read_breeder_paperworks"
            })
        ],

        "load_broker_paperworks": [
            ("transfer", {
                "name": "read_broker_paperworks"
            })
        ],

        "load_trans_paperworks": [
            ("transfer", {
                "name": "read_trans_paperworks"
            })
        ],

        "print_paperwork_breeder_reports": [
            ("transfers", ["get_paperwork_breeder_files"], [
                ("$ext.reports.print_templates", {
                    "printer": "$ext.reports.config['printer_report']",
                    "templates": "$result[0].flin_contents",
                    "formats": ("#map", "$result[0].file_content_type_id", "('pdf' if ($_ == 3)) or ('jrxml/zip' if ($_ == 31))"),
                    "data": None})])
        ],

        "print_paperwork_broker_reports": [
            ("transfers", ["get_paperwork_broker_files"], [
                ("$ext.reports.print_templates", {
                    "printer": "$ext.reports.config['printer_report']",
                    "templates": "$result[0].flin_contents",
                    "formats": ("#map", "$result[0].file_content_type_id", "('pdf' if ($_ == 3)) or ('jrxml/zip' if ($_ == 31))"),
                    "data": None})])
        ],

        "print_paperwork_trans_reports": [
            ("transfers", ["get_paperwork_trans_files"], [
                ("$ext.reports.print_templates", {
                    "printer": "$ext.reports.config['printer_report']",
                    "templates": "$result[0].flin_contents",
                    "formats": ("#map", "$result[0].file_content_type_id", "('pdf' if ($_ == 3)) or ('jrxml/zip' if ($_ == 31))"),
                    "data": None})])
        ],

        "save_receipt_content": [
            ("if", "$storage.receipt_content_id and $storage.receipt_content_id > 0",
                ("transfer", "update_receipt_content"),
                ("transfer", "create_receipt_content")),

            "prepare_update_disclaimer_ids",
            ("if", "$storage.receipt_update_disclaimer_ids",
                ("transfer", "update_receipt_disclaimers")),

            "prepare_create_disclaimer_names",
            ("if", "$storage.receipt_create_disclaimer_names",
                ("transfer", "create_receipt_disclaimers")),
        ],

        # House Accounts

        "pay_now": [
            ("return", {"event": "ha_add_balance",
                        "app": "Cash Register",
                        "data": {
                            "action": TxnType.SALE,
                            "house_account_id": "$g_houseaccount_table.selected.house_account_id",
                            "customer_id": "$storage.id",
                            "balance": "$g_houseaccount_table.selected.hacc_balance",

                            "bundles": [],

                            # product
                            "product_id": ProductId.HOUSE_ACCOUNT_PAYMENT,
                            "price": None,
                            "cost": None,
                            "customer_required": True,
                            "qty": 1,
                            "description": "House Account Payment",
                            "vertical_id": "$g_houseaccount_table.selected.house_account_id"
                        }})
        ],

        # Errors creating/updating entity
        "show_errors": [
            ("if", "$storage.errors#get('data')#get('diag')#get('constraint_name') == 'qp_entity_users_username_not_mfd_uc'",
             [
                 ("$g_manageentity_tabwidget.select_tab", "$g_employeesettings_widget#self"),
                 "$g_displayname_lineedit.set_focus",
                 "$g_displayname_lineedit.set_invalid('This display name already exists.)",
                 ("info_message", "This display name already exists."),
             ],
             ("if", "$storage.errors#get('data')#get('diag')#get('constraint_name') == 'entity_pet_breeders_not_mfd_uc'",
              [
                  ("$g_manageentity_tabwidget.select_tab", "$g_breederinfo_widget#self"),
                  "$g_usda_lineedit.set_focus",
                  "$g_usda_lineedit.set_invalid('Breeder with USDA# already exists.)",
                  ("info_message", "Breeder with USDA# already exists."),
              ],
              ("error_message", "An error occurred while saving. Check that you have the name, " +
               "address, city, state, zip, and country filled in and try " +
               "again.")))
        ],

        # Calculate Weights
        "calculate_weights": [
            ("if", "$g_min_weight_lineedit.data#is_int or $g_min_weight_lineedit.data#is_float",
                [
                    ("push", "$g_min_weight_lineedit", "#update_weight($g_min_weight_lineedit.data, $storage.old_weight_unit ,$g_weightmeasure_combobox.selected.value)"),
                ]),
            ("if", "$g_max_weight_lineedit.data#is_int or $g_max_weight_lineedit.data#is_float",
                [
                    ("push", "$g_max_weight_lineedit", "#update_weight($g_max_weight_lineedit.data, $storage.old_weight_unit ,$g_weightmeasure_combobox.selected.value)"),
                ]),
            ("push", "$storage.old_weight_unit", "$g_weightmeasure_combobox.selected.value"),
        ],

        # Repeat Orders
        "check_address_repeatdelivery": [
            ("if", "$g_addressline1_lineedit.data != '' and $g_city_lineedit.data != '' and $g_zip_lineedit.data != ''",
                ("transfer", "select_repeat_delivery_product"),
                ("info_message", "Address Required for Repeat Orders"))
        ],

        # Website Options
        "create_username": [
            ("call", checkemailtypeid, {"emails": "$email_droplist.data.eml_email",
                                        "email_type_id": "$email_droplist.data.eml_email_type_id",
                                        "email_id": "$email_droplist.data.email_id"},
                ("if", "$result", ("push", "$storage.new_emails", "$result"),
                    ("info_message", "Error in combining lists"))),
            ("push", "$storage.entity_roles", "$c_group_checkcombobox.checked.role_id#add_to_list(-24)"),
            ("if", "$storage.new_emails[1]#length == 0",
                ("info_message", "Email Required"),
                [
                    ("transfer", "save_entity_email", ("transfer", "create_username")),
                ]
            )
        ],
    },

    "fpe_transfers": {
        "get_user_info": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__entity_users",

            "pull": {
                "qp_entity_id": "$storage.user_id",
                "columnsn": "user_roles,user_locations"
            },

            "push": {
                "$storage.user_roles": "$result.user_roles.erole_role_id",
                "$storage.user_locations": "$result.user_locations.ualoc_loc_entity_id"
            },

            "on_start": [
                "$grp_manager_admin_tabs.hide",
                "$grp_customer_manager_admin_tabs.hide",

                "$grp_admin_controls.hide"
            ],

            "on_finish": [
                "show_admin_tabsncontrols",
                ("if", "$app.permissions.has_role(-9,-11)",
                    [
                        "$grp_clerk_controls.show",
                        "$g_breederslug_frame.show",
                    ],
                    ["$g_print_paperwork_breeder_button.show",
                        "$g_print_paperwork_broker_button.show",
                        "$g_print_paperwork_trans_button.show",
                        ("change_state", "g_print_paperwork_breeder_button", {"default_state": "disabled"}),
                        ("change_state", "g_print_paperwork_broker_button", {"default_state": "disabled"}),
                        ("change_state", "g_print_paperwork_trans_button", {"default_state": "disabled"})]),
            ]
        },

        "get_email_types": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/read__tbl__email_types",
            "cache": ("app_cache", "entity_form_read__tbl__email_types", {"days": 1}),

            "pull": {
                "order_by": "> 'emlt_email_type'",
                "columnsn": "email_type_id, emlt_email_type"
            },

            "on_success": [
                ("push", "$storage.temp", "#update_key($result, 'email_type_id','eml_email_type_id')"),
                ("push", "$storage.combobox", "#dict('eml_email_type_id', #dict('match', 'eml_email_type_id',"
                                              "'show', 'emlt_email_type',"
                                              "'data', $storage.temp))"),
                "$email_droplist.set_combox_data($storage.combobox)"
            ]
        },

        "get_basic_roles": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__roles",

            "pull": {
                "qp_role_group": "Entity",
                "_qp_entity_id": "$storage.id or None",

                "order_by": "> 'role_name'",
            },

            "push": {
                "$storage.roles": "$result#reject('role_name', 'Contact', 'Pinogy', 'Location', 'Client',"
                                  " 'Warehouse')"
            },

            "on_success": [
                # ("if", "$app.permissions.has_role(-8,-9,-11)",
                #     ("push", "$c_group_checkcombobox", "$storage.roles"),
                ("push", "$c_group_checkcombobox", "$storage.roles"),
            ],

            "on_finish": [
                "set_default_values",
                "$g_entityfirst_lineedit.set_focus"
            ]
        },

        "get_regions": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/read__tbl__postal_regions",

            # "cache": ("app_cache", "postal", {"days": 3}),

            "pull": {
                "qp_prgn_country_id": "$g_country_combobox.selected.country_id",

                "order_by": "> 'prgn_iso_code'",
                "columnsn": "postal_region_id,prgn_iso_code,prgn_full_name,prgn_country_id"
            },

            "push": {
                "$g_state_combobox": "$result",
                "$storage.region_ids": "$result",
            },

            "on_finish": [
                ("if", "$storage.entity_info",
                    ("push", "$g_state_combobox.selected", "$storage.entity_info.ent_addresses#any#get('addr_region_id','')"))
            ]
        },

        "get_countries": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/read__tbl__countries",

            "cache": ("app_cache", "countries", {"days": 3}),

            "pull": {
                "order_by": "> 'ctry_sort_value'",

                "columnsn": "country_id,ctry_full_name,ctry_sort_value"
            },

            "push": {
                "$g_country_combobox": "$result",
                "$storage.country_ids": "$result",
            },

            "on_finish": [
                ("if", "$g_country_combobox.is_selected", ("transfer", "get_regions"))
                # ("$g_state_combobox.filter", {"prgn_country_id": "$g_country_combobox.selected.country_id"})
            ]
        },

        "select_role": {
            "dialog": {
                "name": "ManageRole",

                "data": {
                    "flags": ["update", "role"],
                    "entity_id": "$storage.id or None",
                    "role_id": "$form.input.role_id",
                    "user_id": "$storage.user_id"
                }
            },

            "on_start": [
                ("change_state", "grp_command_buttons", {"default_state": "disabled"}),
            ],

            "on_finish": [
                ("transfer", "get_basic_roles",
                    [
                        ("transfer", "get_basic_roles",
                            ("if", "$storage.entity_roles != None",
                                "$c_group_checkcombobox.set_checked($storage.roles#where_list('role_id', $storage.entity_roles).role_name, True)")),
                        # "set_default_values",
                        ("change_state", "grp_command_buttons", {"default_state": "enabled"})
                    ])

            ]
        },

        #    Entity Transfers

        "get_entity": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__entities",

            "convert": "$result[0]",

            "pull": {
                "qp_entity_id": "$storage.id",
                "_qp_loc_entity_id": "$storage.loc_entity_id",
            },

            "push": {
                "$storage.entity_info": "$result"
            },

            "on_start": ["disable_buttons"],

            "on_success": [
                "push_entity",
                "push_entity_subtype",
                "hide_role_controls",
                ("if", "'update' in $storage.status",
                    ("if", "$app.permissions.has_role(-9,-11)",
                        [
                            ("change_state", "grp_manager_controls", {"default_state": "enabled"}),
                            ("if", "$g_employeerole_combobox.selected.role_name == 'Lite User'",
                             [
                                 "$g_moduleaccess_list.uncheck_all",
                                 ("push", "$g_moduleaccess_list.selected", [8]),
                                 ("change_state", "g_moduleaccess_list", {"default_state": "disabled"}),
                             ],
                             [
                                 ("change_state", "g_moduleaccess_list", {"default_state": "enabled"})
                             ]),
                        ],
                        ("if", "$storage.user_locations#flat#intersect($result.ent_user_locations.ualoc_loc_entity_id)#length > 0 or "
                               "'from_entity' in $form.input.flags",
                            [
                                ("change_state", "grp_manager_controls", {"default_state": "enabled"}),
                                ("if", "$g_employeerole_combobox.selected.role_name == 'Lite User'",
                                 [
                                     "$g_moduleaccess_list.uncheck_all",
                                     ("push", "$g_moduleaccess_list.selected", [8]),
                                     ("change_state", "g_moduleaccess_list", {"default_state": "disabled"}),
                                 ],
                                 [
                                     ("change_state", "g_moduleaccess_list", {"default_state": "enabled"})
                                 ]),
                            ],
                            ("change_state", "grp_manager_controls", {"default_state": "disabled"})))),

            ],

            "on_finish": [
                {"case": {
                            "value": "$c_group_checkcombobox.checked.role_name",

                            "Supplier": [
                                ("if", "-18 not in $storage.entity_roles",
                                    ("transfer", "get_pidb3_suppliers"))
                            ],
                }},
                "load_entity_datasets",
                "show_role_controls"
            ],
        },

        "get_employee_check": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/read__qpt__billing_check_users",

            "pull": {
                "qp_mode": "Add",
            },

            "on_success": [
                ("if", "$result.billing_price[0] != None or $result.billing_discount[0] ==",
                    [
                        ("if", "$result.billing_price[0]#float > $result.billing_discount[0]#float",
                    [
                        ("if", "$result.billing_periodicity[0] == 'YEAR'", ("push", "$storage.period", "yearly"),
                            ("if", "$result.billing_periodicity[0] == 'MONTH'", ("push", "$storage.period", "monthly"),
                                ("if", "$result.billing_periodicity[0] == 'WEEK'", ("push", "$storage.period", "weekly")))),

                        ("push", "$storage.employeetype", ''),
                        ("if", "$g_employeerole_combobox.selected.role_name == 'Lite User'",
                            ("push", "$storage.employeetype", 'Lite User ')),

                        {"confirm": {
                            "text": "> This change will increase your {$storage.employeetype}employee count from {$result.billing_current_qty[0]#quantity}"
                                    " to {$result.billing_new_qty[0]#quantity} employees,"
                                    " and will add ${$result.billing_full_price[0]#currency(2,0)} to your {$storage.period} invoice."
                                    "  \nA prorated charge of ${$result.billing_price[0]#currency(2,0)} will be invoiced immediately for"
                                    " {#datetime.strftime('%m/%d')} - {$result.billing_period_end[0]#datetime.strftime('%m/%d')}.",
                            "yes_text": "Confirm",
                            "no_text": "Reject",

                            "on_accept": [
                                ("if", "'from_entity' in $form.input.flags",
                                    "save_new_entity_2",
                                    ("transfer", "save_new_entity",
                                        ("if", "$storage.close",
                                            "$form.close",
                                            [("push", "$storage.close", "True"), ("push", "$storage.status[0]", "update"), "load_data"]))),
                                ]
                            }}
                        ],
                        [
                            ("if", "'from_entity' in $form.input.flags",
                                "save_new_entity_2",
                                ("transfer", "save_new_entity",
                                    ("if", "$storage.close",
                                        "$form.close",
                                                [("push", "$storage.close", "True"), ("push", "$storage.status[0]", "update"), "load_data"]))),
                     ]),
                    ],
                    [
                        ("if", "'from_entity' in $form.input.flags",
                            "save_new_entity_2",
                            ("transfer", "save_new_entity",
                                ("if", "$storage.close",
                                    "$form.close",
                                    [("push", "$storage.close", "True"), ("push", "$storage.status[0]", "update"),
                                    "load_data"]))),

                    ]
                )
            ]
        },

        "get_employee_lite_check": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/read__qpt__billing_check_lite_users",

            "pull": {
                "qp_mode": "Add",
            },

            "on_success": [
                ("if", "$result.billing_price[0] != None or $result.billing_discount[0] ==",
                    [
                        ("if", "$result.billing_price[0]#float > $result.billing_discount[0]#float",
                            [
                                ("if", "$result.billing_periodicity[0] == 'YEAR'", ("push", "$storage.period", "yearly"),
                                ("if", "$result.billing_periodicity[0] == 'MONTH'", ("push", "$storage.period", "monthly"),
                                ("if", "$result.billing_periodicity[0] == 'WEEK'", ("push", "$storage.period", "weekly")))),

                                ("push", "$storage.employeetype", ''),
                                ("if", "$g_employeerole_combobox.selected.role_name == 'Lite User'",
                                 ("push", "$storage.employeetype", 'Lite User ')),

                                 {"confirm": {
                                     "text": "> This change will increase your {$storage.employeetype}employee count from {$result.billing_current_qty[0]#quantity}"
                                             " to {$result.billing_new_qty[0]#quantity} employees,"
                                             " and will add ${$result.billing_full_price[0]#currency(2,0)} to your {$storage.period} invoice."
                                             "  \nA prorated charge of ${$result.billing_price[0]#currency(2,0)} will be invoiced immediately for"
                                             " {#datetime.strftime('%m/%d')} - {$result.billing_period_end[0]#datetime.strftime('%m/%d')}.",
                                     "yes_text": "Confirm",
                                     "no_text": "Reject",

                                     "on_accept": [
                                         ("if", "'from_entity' in $form.input.flags",
                                          "save_new_entity_2",
                                          ("transfer", "save_new_entity",
                                           ("if", "$storage.close",
                                            "$form.close",
                                            [("push", "$storage.close", "True"), ("push", "$storage.status[0]", "update"),
                                             "load_data"]))),
                                     ]
                                 }}
                            ],
                            [
                                 ("if", "'from_entity' in $form.input.flags",
                                  "save_new_entity_2",
                                  ("transfer", "save_new_entity",
                                   ("if", "$storage.close",
                                    "$form.close",
                                    [("push", "$storage.close", "True"), ("push", "$storage.status[0]", "update"), "load_data"]))),
                            ])
                 ],
                 [
                     ("if", "'from_entity' in $form.input.flags",
                      "save_new_entity_2",
                      ("transfer", "save_new_entity",
                       ("if", "$storage.close",
                        "$form.close",
                        [("push", "$storage.close", "True"), ("push", "$storage.status[0]", "update"),
                         "load_data"]))),

                 ]
                 )
            ]
        },

        "save_new_entity": {
            "method": "post",
            "resource": "/apps/any/entities",

            "on_start": ["disable_buttons", "pull_entity", "pull_entity_subtype"],

            "on_finish": [
                ("push", "$storage.id", "$result.entity_id"),
                ("if", "$storage.status[1] == 'location'", "save_receipt_content"),
                ("return", {"id": "$result.entity_id"})
            ],

            "on_failure": [
                ("if", "$result", [("push", "$storage.errors","$result"), "show_errors"]),
                ("change_state", "grp_command_buttons", {"default_state": "enabled"}),
            ]
        },

        "save_existing_entity": {
            "method": "put",
            "resource": "/apps/any/entities/{id}",

            "query": {
                "id": "$storage.id",
            },

            "on_start": ["disable_buttons", "pull_entity_ids", "pull_entity", "pull_entity_subtype"],
            "on_finish": ("if", "$storage.status[1] == 'location'", "save_receipt_content"),

            "on_failure": [
                ("if", "$result", [("push", "$storage.errors", "$result"), "show_errors"]),
                ("change_state", "grp_command_buttons", {"default_state": "enabled"}),
                "$paginator.stop"
            ]
        },

        "delete_entity": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/delete__tbl__entities",

            "pull": {
                "qp_entity_id": "$storage.id"
            }
        },

        "duplicate_entity_numbers": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__search_telephone",

            "pull": {
                "qp_cnum_numbers": "$phone_droplist.last_line_update"
            },

            "on_finish": [
                ("if", "$result",
                    [
                        ("if", "$storage.id not in $result#get('entity_id')",
                            [
                                ("push", "$storage.duplicate_number", "$result"),
                                ("push", "$storage.temp", "> This number is already associated with: \n " +
                                                          "{$result.ent_name#str.replace('[', '').replace(']', '').replace(',', '\n')}" +
                                                          "\nEdit that existing record to prevent a duplicate."),
                                "$phone_droplist.set_invalid($storage.temp)"
                            ])
                    ]),

            ],
        },

        "duplicate_email": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__search_email",

            "pull": {
                "qp_eml_emails": "$email_droplist.last_line_update"
            },

            "on_finish": [
                ("if", "$result",
                    [
                        ("push", "$storage.temp", "> This Email is already associated with: \n " +
                        "{$result.ent_name#str.replace('[', '').replace(']', '').replace(',', '\n')}" +
                        "\nEdit that existing record to prevent a duplicate."),
                        "$email_droplist.set_invalid($storage.temp)"
                    ]),

            ],
        },

        # Supplier
        "get_supplier_info": {
            "method": "get",
            "resource": "/api/supplier_locations",
            "content_type": "json",

            "pull": {
                "location_id": "$storage.location_id",
                "supplier_id": "$form.input.ids[$paginator.index]",
                "fields": ["supplier", "supplier.pidb_supplier_id", "*", "location.*"]
            },

            "push": {
                "$g_accountnumber_lineedit": "$result[0].account_number or ''",
                "$g_leadtime_lineedit": "$result[0].lead_time_days or '3'",
                "$g_orderfrequency_lineedit": "$result[0].order_frequency or '7'",
                "$g_minorderamount_lineedit": "$result[0].min_order_amount or '0'",
                "$g_supplierinfosendvia_combobox.selected": "$result[0].send_po_method",
                "$g_suppliernotes_plaintextedit": "$result[0].notes or ''",
                "$g_lastsync_lineedit": "($result[0].pidb_last_sync_at#date.strftime('%Y-%m-%d') if "
                                        "$result[0].pidb_last_sync_at !=None) or ''",
                "$storage.sup_loc_entity_id": "$result[0].location_id or None",
                "$storage.supplier_location_id": "$result[0].id or ''",
                "$g_verification_combobox.selected": "$result[0].receive_verify_behavior or 'Optional'",
                "$g_enablepidb_checkBox": "$result[0].has_pidb_catalogs",
                "$g_weightmeasure_combobox.selected": "$result[0].order_weight_unit",
                "$storage.old_weight_unit": "$result[0].order_weight_unit",
            },

            "on_success": [
                # Calculate Weight and Dimensions
                ("push", "$g_min_weight_lineedit", "(#set_weight($result[0].order_min_weight, "
                                                    "$result[0].order_weight_unit)#quantity if $result[0].order_min_weight != None) or ''"),

                ("push", "$g_max_weight_lineedit", "(#set_weight($result[0].order_max_weight, "
                                                   "$result[0].order_weight_unit)#quantity if $result[0].order_max_weight != None) or ''"),

            ],

            "on_finish": [
                ("if", "$app.integrations.is_enabled(" + str(Integrations.PIDBSUPPLIERS) + ") == True",
                    "$g_pidb_groupbox.show",
                    "$g_pidb_groupbox.hide")
            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "get_corp_supplier_info": {
            "method": "get",
            "resource": "/api/supplier_locations",
            "content_type": "json",

            "pull": {
                "supplier_id": "$form.input.ids[$paginator.index]",
                "fields": ["supplier", "supplier.pidb_supplier_id", "*", "location.*"]
            },

            "push": {
                "$g_supplierinfo_table": "$result",
                "$storage.location_loc_ids": "$result.location_id"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "create_current_multi_loc_supplier": {
            "method": "put",
            "resource": "/api/supplier_locations",
            "content_type": "json",

            "pull": {
                "supplier_ids": ["$storage.id"],
                "location_ids": ["$storage.location_id"],
                "account_number": "$g_accountnumber_lineedit or ''",
                "lead_time_days": "$g_leadtime_lineedit",
                "order_frequency": "$g_orderfrequency_lineedit",
                "notes": "$g_suppliernotes_plaintextedit or ''",
                "min_order_amount": "$g_minorderamount_lineedit",
                "send_po_method": "$g_supplierinfosendvia_combobox.selected.value",
                "receive_verify_behavior": "$g_verification_combobox.selected.value or 'Optional'",
                "order_min_weight": "(#weight_to_grams($g_min_weight_lineedit.data, $g_min_weight_lineedit.selected.value) "
                                    "if $g_prod_weight_lineedit.data != '') or None",
                "order_max_weight": "(#weight_to_grams($g_max_weight_lineedit.data, $g_max_weight_lineedit.selected.value) "
                                    "if $g_prod_weight_lineedit.data != '') or None",
                "order_weight_unit": "$g_weightmeasure_combobox.selected.value"

            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "update_current_entity_loc_supplier": {
            "method": "put",
            "resource": "/api/supplier_locations",
            "content_type": "json",

            "pull": {
                "supplier_ids": ["$storage.id"],
                "location_ids": ["$storage.sup_loc_entity_id"],
                "account_number": "$g_accountnumber_lineedit or ''",
                "lead_time_days": "$g_leadtime_lineedit",
                "order_frequency": "$g_orderfrequency_lineedit",
                "notes": "$g_suppliernotes_plaintextedit or ''",
                "min_order_amount": "$g_minorderamount_lineedit",
                "send_po_method": "$g_supplierinfosendvia_combobox.selected.value",
                "receive_verify_behavior": "$g_verification_combobox.selected.value or 'Optional'",
                "has_pidb_catalogs": "$g_enablepidb_checkBox.data",
                "order_min_weight": "(#weight_to_grams($g_min_weight_lineedit.data, $g_min_weight_lineedit.selected.value) "
                    "if $g_prod_weight_lineedit.data != '') or None",
                "order_max_weight": "(#weight_to_grams($g_max_weight_lineedit.data, $g_max_weight_lineedit.selected.value) "
                    "if $g_prod_weight_lineedit.data != '') or None",
                "order_weight_unit": "$g_weightmeasure_combobox.selected.value"

            },
            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "delete_current_loc_supplier": {
            "method": "post",
            "resource": "/apps/any/queries/delete__tbl__entity_supplier_locations",

            "pull": {
                "qp_supplier_location_id": "$g_supplierinfo_table.all_selected.id"
            },

            "on_success": [
                ("transfer", "get_corp_supplier_info")
            ],

            "on_failure": [
                ("if", "$result",
                    ("if", "$result#get('data')#get('diag')#get('constraint_name') == 'check_supplier_used'",
                        ("info_message", "Supplier cannot be removed because there are open Purchases."),
                        ("error_message", "'\n'.join($errors)"))),
            ]
        },

        "create_pidb_loc_supplier": {
            "dialog": {
                "name": "ManageFullSupplierDialog",

                "data": {
                    "flags": ["create"],
                    "loc_ids": "$storage.id",
                    "current_locations": "$storage.location_loc_ids",
                }
            },

            "on_finish": [ ("transfer", "get_corp_supplier_info")
            ]
        },

        "update_pidb_loc_supplier": {
            "dialog": {
                "name": "ManageFullSupplierDialog",

                "data": {
                    "flags": ["update"],
                    "loc_ids": "$g_supplierinfo_table.all_selected.supplier_id",
                    "loc_entity_ids": "$g_supplierinfo_table.all_selected.location_id",
                    "entity_ids": "$g_supplierinfo_table.all_selected.supplier_id"
                }
            },

            "on_finish": [
                ("transfer", "get_corp_supplier_info"),
                # ("transfer", "get_supplier_info"),
                # ("transfer", "get_pidb3_suppliers")
            ]
        },

        "massaction_pidb_loc_supplier": {
            "dialog": {
                "name": "ManageFullSupplierDialog",

                "data": {
                    "flags": ["massaction"],
                    "loc_ids": "$g_supplierinfo_table.all_selected.supplier_id",
                    "loc_entity_ids": "$g_supplierinfo_table.all_selected.location_id",
                    "entity_ids": "$g_supplierinfo_table.all_selected.supplier_id"
                }
            },

            "on_finish": [
                ("transfer", "get_corp_supplier_info"),
                ("transfer", "get_supplier_info"),
                ("transfer", "get_pidb3_suppliers")
            ]
        },

        # Emergency Contacts
        "read_emergency_contact": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__entity_contacts",

            "pull": {
                "qp_econt_entity_id": "$storage.id",
                "order_by": "> [('{$g_emergencycontact_table.sort_field}', '{$g_emergencycontact_table.sort_order}')]",
            },

            "push": {
                "$g_emergencycontact_table": "$result",
            }
        },

        "create_emergency_contact": {
            "dialog": {
                "name": "ManageEmergencyContactDialog",

                "data": {
                    "flags": ["create"],
                    "entity_id": "$storage.id",
                    "region_id": "$storage.region_id",
                    "country_id": "$storage.country_id"
                }
            },
            "on_finish": ("transfer", "read_emergency_contact")
        },

        "update_emergency_contact": {
            "dialog": {
                "name": "ManageEmergencyContactDialog",

                "data": {
                    "flags": ["update"],
                    "entity_id": "$storage.id",
                    "ids": "$g_emergencycontact_table.all_selected.entity_contact_id",
                    "region_id": "$storage.region_id",
                    "country_id": "$storage.country_id"
                }
            },
            "on_finish": ("transfer", "read_emergency_contact")
        },

        "delete_emergency_contact": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/delete__tbl__entity_contacts",

            "pull": {
                "qp_entity_contact_id": "$g_emergencycontact_table.all_selected.entity_contact_id",
            },

            "on_finish": ("transfer", "read_emergency_contact")
        },

        # Entity Address
        "get_entity_api": {
            "method": "get",
            "resource": "/api/entities/{id}",
            "content_type": "json",

            "query": {
                "id": "$storage.id"
            },

            "pull": {
                "fields": [
                    "postal_addresses",
                    "postal_addresses.type"
                ],
            },

            "push": {
                "$g_address_table": "$result.postal_addresses"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "add_entity_address": {
            "dialog": {
                "name": "ManageEntityAddressDialog",

                "data": {
                    "flags": ["create"],
                    "entity_id": "$storage.id",
                    "country_id": "$storage.country_id",
                    "address_count": "$g_address_table.data"
                }
            },
            "on_finish": ("transfers", ["get_entity","get_entity_api"])
        },

        "update_entity_address": {
            "dialog": {
                "name": "ManageEntityAddressDialog",

                "data": {
                    "flags": ["update"],
                    "entity_id": "$storage.id",
                    "address": "$g_address_table.all_selected",
                    "country_id": "$storage.country_id",
                }
            },
            "on_finish": ("transfers", ["get_entity","get_entity_api"])
        },

        "delete_entity_address": {
            "method": "put",
            "resource": "/api/entities/{id}",
            "content_type": "json",

            "query": {
                "id": "$storage.id"
            },

            "pull": {
                "postal_addresses": "$storage.address_ids",
                "delete_missing_postal_addresses": False,
            },

            "on_finish": ("transfers", ["get_entity", "get_entity_api"]),

            "on_failure": [
                ("if", "$result#get('data')#get('diag')#get('message_primary') == 'Cannot remove an address assigned to a recurring order'",
                 [
                     ("info_message", "$result#get('data')#get('diag')#get('message_detail')"),
                 ],
                 ("error_message", "An error occurred when saving the record!"))
            ]

        },

        "primary_entity_address": {
            "method": "put",
            "resource": "/api/entities/{id}",
            "content_type": "json",

            "query": {
                "id": "$storage.id"
            },

            "on_start": [
                ("push", "$storage.add_type_id", "$g_address_table.selected.type_id")
            ],

            "pull": {
                "postal_addresses": {
                    "id": "$g_address_table.selected.id",
                    "is_primary": True,
                    "type_id": "$g_address_table.selected.type_id",
                },
                # ("#map","$g_address_table.selected.id","#dict('id',$_,'is_primary',True,'type_id',$storage.add_type_id)"),
                "delete_missing_postal_addresses": False,
            },

            "on_finish": ("transfers", ["get_entity","get_entity_api"]),

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        # Show Saved Payments
        "get_show_saved_payments": {
            "method": "post",
            "resource": "/apps/cash_register/queries/read__tbl__payment_gateways",

            "pull": {
                "qp_pgate_loc_entity_id": "$storage.location_id",
                "qp_pgate_payment_processor_id": 8,
            },

            "push": {
            },

            "on_success": [
                ("if", "$result.length == 0",
                    "$g_manageentity_tabwidget.hide_tab('g_savedcards_widget')",
                    "$g_manageentity_tabwidget.show_tab('g_savedcards_widget')")
            ]
        },

        # Frequent Buyer
        "get_buyer_program_status": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__customer_frequent_buyer_status",

            "pull": {
                "qp_cust_entity_id": "$storage.id"
            },

            "push": {
                "$g_programstatus_table": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        "manage_buyer_program": {
            "dialog": {
                "name": "ManageFrequentBuyerDialog",

                "data": {
                    "flags": ["update"],
                    "new_promotion": False,
                    "customer_id": "$storage.id",
                    "promotion_id": "$g_programstatus_table.selected.promotion_id",
                }
            },
            "on_finish": ("transfer", "get_buyer_program_status")
        },

        "create_buyer_program": {
            "dialog": {
                "name": "ManageFrequentBuyerDialog",

                "data": {
                    "new_promotion": True,
                    "customer_id": "$storage.id",
                    "promotion_id": "$g_programstatus_table.selected.promotion_id or None",
                }
            },
            "on_finish": ("transfer", "get_buyer_program_status")
        },

        "get_rewards_earned": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__customer_frequent_buyer_history",

            "pull": {
                "qp_cust_entity_id": "$storage.id"
            },

            "push": {
                "$g_rewardsearned_table": "$result"
            },

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        #   Loyalty
        "get_bonusbucks": {
            "method": "get",
            "resource": "/api/loyalty_customers/{id}",
            "content_type": "json",

            "query": {
                "id": "$storage.id"
            },

            "push": {
                "$g_loyaltybalance_lineedit": "$result.balance_bb"
            },

            "on_finish": [
                ("if", "'error' in $result#keys",
                    [
                        ("info_message", "$result#get('error')#get('message')")
                    ]),
            ],
        },

        "update_bonusbucks": {
            "dialog": {
                "name": "ManageBonusBucks",

                "data": {
                    "flags": ["update"],
                    "balance": "$g_loyaltybalance_lineedit",
                    "entity_id": "$storage.id",
                }
            },

            "on_finish": [
                ("if", "$dialog.output.balance != '' and $dialog.output.balance != None",
                    ("push", "$g_loyaltybalance_lineedit", "$dialog.output.balance")),
            ]

        },

        "get_pidb3_suppliers": {
            "method": "get",
            "resource": "/api/pidb_suppliers",

            "on_finish": [
                ("push", "$storage.pidb3", "$result"),
                ("transfer", "get_pidb")
            ],

            "on_failure": ("error_message", "'\n'.join($errors)")
        },

        #  PIDB
        "get_pidb": {
            "resource": "/api/pidb_supplier_mapping/{local_supplier_id}",
            "method": "get",

            "query": {
                "local_supplier_id": "$storage.id",
                "location_id": "$storage.location_id"
            },

            "on_finish": [
                ("push", "$g_pidbmappedto_lineedit", ""),
                ("if", "$result",
                    ("if", "$result.supplier_id != None",
                        ("push", "$g_pidbmappedto_lineedit", "$storage.pidb3#where('id', $result.supplier_id).name[0]")))
            ]
        },

        #    Employee Transfers

        "get_all_user_roles": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/read__tbl__roles",

            "pull": {
                "qp_role_group": "User",
                "columnsn": "role_id,role_name",
                "order_by": "> 'role_name'"
            },

            "push": {

            },

            "on_success": [
                ("if", "$app.permissions.has_role(-9,-11)",
                 ("push", "$g_employeerole_combobox",
                  "$result#reject('role_name', 'Contact', 'Pinogy', 'Integration', 'Web Customer')"),
                 [
                     ("if", "$app.permissions.has_role(-8)",
                      ("push", "$g_employeerole_combobox",
                       "$result#reject('role_name', 'Contact', 'Pinogy', 'Integration', 'Admin', 'Web Customer', 'Admin', 'Manager')"),
                      ("push", "$g_employeerole_combobox",
                       "$result#reject('role_name', 'Contact', 'Pinogy', 'Integration', 'Admin', 'Web Customer')")),

                     # ("push", "$g_employeerole_combobox", "$result#reject('role_name', 'Contact', 'Pinogy', 'Integration', 'Admin', 'Web Customer')")
                 ]
                 )
                # ("if", "$app.permissions.has_role(-9,-11)",
                #     ("push", "$g_employeerole_combobox", "$result#reject('role_name', 'Contact', 'Pinogy', 'Integration', 'Web Customer')"),
                #     ("push", "$g_employeerole_combobox", "$result#reject('role_name', 'Contact', 'Pinogy', 'Integration', 'Admin', 'Web Customer')"))
            ]
        },

        "update_permissions": {
            "dialog": {
                "name": "ManagePermissionsDialog",

                "data": {
                    "flags": [],
                    "role_name": "$g_employeerole_combobox.selected.role_name",
                    "role_id": "$g_employeerole_combobox.selected.role_id"
                }
            },

            "on_finish": "load_broker_paperworks"
        },

        "get_all_apps": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__apps",

            "pull": {
                "columnsn": "app_id,app_name",
                "order_by": "> 'app_name'"
            },

            "push": {
                "$g_moduleaccess_list": "$result#reject('app_id', 2,10,12,13)"
            },

            "on_success": [
                ("if", "$app.lite == True",
                    [
                        ("push", "$g_moduleaccess_list.readonly_items", [3,6,7,8,9,11])
                    ]),
            ],
        },

        "get_all_locations": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/get_locations",

            "pull": {
                "qp_loc_is_enabled": True,
                "columnsn": "loc_entity_id,ent_name,entity_address_info",
                "order_by": "> 'ent_name'"
            },

            "push": {
                "$storage.locations": "$result",
                "$g_locationaccess_list": "$result",
            },

            "on_success": [
                {"push": {
                    "$storage.country_id":
                        "$result#find_where('loc_entity_id',$storage.location_id).entity_address_info#any#get('addr_country_id',None)",
                    "$storage.region_id":
                        "$result#find_where('loc_entity_id',$storage.location_id).entity_address_info#any#get('addr_region_id',None)"
                }},
                ("if", "$app.permissions.has_role(-8,-19)",
                 ("if", "$storage.locations.loc_entity_id#non_matches($storage.user_locations[0])[0]#length > 0",
                  [
                      ("push", "$g_locationaccess_list.readonly_items",
                       "$storage.locations.loc_entity_id#non_matches($storage.user_locations[0])[0]")
                  ])),

            ]
        },

        "get_locations": {
            "method": "post",
            "resource": "/apps/cash_register/queries/read__qpt__list_locations",
            "cache": ("app_cache", "emp_all_locations", {"days": 1}),

            "pull": {
                "order_by": "> 'loc_report_code'",
                "columns": "loc_entity_id,ent_business_name,ent_name,loc_receipt_name,loc_report_code,loc_slug,"
                           "loc_is_enabled,ppg_name,loc_show_regional"
            },

            "push": {
                "$g_location_repeat_orders_combobox": "$result",
                "$g_emplocations_combobox": "$result",
            },

            "on_success": [
                ("push", "$g_location_repeat_orders_combobox.selected", "$storage.location_id"),
                ("if", "$result#length == 1",
                    [
                        "$g_emplocations_combobox,hide",
                        "$g_emplocations_label.hide",
                    ]),
                ("if", "$app.permissions.has_role(-9) == False",
                    ("change_state", "g_emplocations_combobox", {"default_state": "disabled"})),
             ]
        },

        "duplicate_display_name": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__list_employees",

            "pull": {
                "qp_user_username": "$g_displayname_lineedit",
                "columns": "user_username,user_entity_id"
            },

            "on_finish": [
                ("if", "$result",
                 [
                     ("if", "$result[0].user_entity_id != $storage.id",
                        [
                            ("push", "$storage.duplicate_display_name", "$result"),
                            ("push", "$storage.temp", "> This Display Name already exists."),
                            "$g_displayname_lineedit.set_invalid($storage.temp)"
                        ])
                 ]),

            ],
        },

        #    Breeder Transfers

        "read_breeder_paperworks": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__pet_paperwork",

            "pull": {
                "qp_ppw_pbreeder_entity_ids": "$storage.id",

                "order_by": "> [('{$g_paperwork_breeder_table.sort_field}', '{$g_paperwork_breeder_table.sort_order}')]",

                "limit": "$g_paperworks_breeder_paginator.limit",
                "offset": "$g_paperworks_breeder_paginator.offset",

                "columnsn": "pet_paperwork_id,ppw_paperwork_displayname,ppw_file_id," +
                            "ppwc_name,ppw_in_print_qty,ppw_out_print_qty"
            },

            "push": {
                "$g_paperwork_breeder_table": "$result",
                "$g_paperworks_breeder_paginator.total": "$total",
                "$g_totalresults_breeder_label": "> {$total} results found. Select one or more rows to make changes."
            }
        },

        "create_breeder_paperwork": {
            "dialog": {
                "name": "ManagePaperwork",

                "data": {
                    "flags": ["create", "paperwork", "breeder"],
                    "entity_id": "$storage.id"
                }
            },
            "on_finish": "load_breeder_paperworks"
        },

        "update_breeder_paperwork": {
            "dialog": {
                "name": "ManagePaperwork",

                "data": {
                    "flags": ["update", "paperwork", "breeder"],
                    "ids": "$g_paperwork_breeder_table.all_selected.pet_paperwork_id",
                    "entity_id": "$storage.id"
                }
            },
            "on_finish": "load_breeder_paperworks"
        },

        "delete_breeder_paperwork": {
            "method": "delete",
            "resource": "/apps/pet_tracker/paperworks/{id}",

            "query": {
                "id": "$g_paperwork_breeder_table.all_selected.pet_paperwork_id#str.strip('[]').replace(' ', '')"
            },

            "on_start": ("$g_paperwork_breeder_table.remove_row", "$g_paperwork_breeder_table.all_selected"),
            "on_finish": "load_breeder_paperworks"
        },

        # Get Paperwork to print

        "get_paperwork_breeder_file": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__tbl__vw_files_with_in_band_contents",

            "convert": "$result[0]",

            "pull": {
                "qp_file_id": "$iter"
            }
        },

        "get_paperwork_breeder_files": {
            "type": "iterator",

            "iterator": "$g_paperwork_breeder_table.all_selected.ppw_file_id",
            "transfer": "get_paperwork_breeder_file",
        },

        "get_paperwork_broker_file": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__tbl__vw_files_with_in_band_contents",

            "convert": "$result[0]",

            "pull": {
                "qp_file_id": "$iter"
            }
        },

        "get_paperwork_broker_files": {
            "type": "iterator",

            "iterator": "$g_paperwork_broker_table.all_selected.ppw_file_id",
            "transfer": "get_paperwork_broker_file",
        },

        "get_paperwork_trans_file": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__tbl__vw_files_with_in_band_contents",

            "convert": "$result[0]",

            "pull": {
                "qp_file_id": "$iter"
            }
        },

        "get_paperwork_trans_files": {
            "type": "iterator",

            "iterator": "$g_paperwork_trans_table.all_selected.ppw_file_id",
            "transfer": "get_paperwork_trans_file",
        },

        #    Broker Transfers

        "read_broker_paperworks": {
            "type": "row_by_row",

            "table": "g_paperwork_broker_table",
            "match": "pet_paperwork_id",

            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__pet_paperwork",

            "pull": {
                "qp_ppw_pbroker_entity_ids": "$storage.id",

                "order_by": "> [('{$g_paperwork_broker_table.sort_field}', '{$g_paperwork_broker_table.sort_order}')]",

                "limit": "$g_paperworks_broker_paginator.limit",
                "offset": "$g_paperworks_broker_paginator.offset",

                "columnsn": "pet_paperwork_id,ppw_paperwork_displayname,ppw_file_id," +
                           "ppwc_name,ppw_in_print_qty,ppw_out_print_qty"
            },

            "push": {
                "$g_paperwork_broker_table": "$result",
                "$g_paperworks_broker_paginator.total": "$total",
                "$g_totalresults_broker_label": "> {$total} results found. Select one or more rows to make changes."
            }
        },

        "create_broker_paperwork": {
            "dialog": {
                "name": "ManagePaperwork",
                "data": {
                    "flags": ["create", "paperwork", "broker"],
                    "entity_id": "$storage.id"
                }
            },

            "on_finish": "load_broker_paperworks"
        },

        "update_broker_paperwork": {
            "dialog": {
                "name": "ManagePaperwork",

                "data": {
                    "flags": ["update", "paperwork", "broker"],
                    "ids": "$g_paperwork_broker_table.all_selected.pet_paperwork_id",
                    "entity_id": "$storage.id"
                }
            },

            "on_finish": "load_broker_paperworks"
        },

        "delete_broker_paperwork": {
            "method": "delete",
            "resource": "/apps/pet_tracker/paperworks/{id}",

            "query": {
                "id": "$g_paperwork_broker_table.all_selected.pet_paperwork_id#str.strip('[]').replace(' ', '')"
            },

            "on_start": ("$g_paperwork_broker_table.remove_row", "$g_paperwork_broker_table.all_selected"),
            "on_finish": "load_broker_paperworks"
        },

        #    Transporter Transfers

        "read_trans_paperworks": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__pet_paperwork",

            "pull": {
                "qp_ppw_ptrnsp_entity_ids": "$storage.id",

                "order_by": "> [('{$g_paperwork_trans_table.sort_field}', '{$g_paperwork_trans_table.sort_order}')]",

                "limit": "$g_paperworks_trans_paginator.limit",
                "offset": "$g_paperworks_trans_paginator.offset",

                "columnsn": "pet_paperwork_id,ppw_paperwork_displayname,ppw_file_id," +
                            "ppwc_name,ppw_in_print_qty,ppw_out_print_qty"
            },

            "push": {
                "$g_paperwork_trans_table": "$result",
                "$g_paperworks_trans_paginator.total": "$total",
                "$g_totalresults_trans_label": "> {$total} results found. Select one or more rows to make changes."
            }
        },

        "create_trans_paperwork": {
            "dialog": {
                "name": "ManagePaperwork",
                "data": {
                    "flags": ["create", "paperwork", "transporter"],
                    "entity_id": "$storage.id"
                }
            },

            "on_finish": "load_trans_paperworks"
        },

        "update_trans_paperwork": {
            "dialog": {
                "name": "ManagePaperwork",

                "data": {
                    "flags": ["update", "paperwork", "transporter"],
                    "ids": "$g_paperwork_trans_table.all_selected.pet_paperwork_id",
                    "entity_id": "$storage.id"
                }
            },

            "on_finish": "load_trans_paperworks"
        },

        "delete_trans_paperwork": {
            "method": "delete",
            "resource": "/apps/pet_tracker/paperworks/{id}",

            "query": {
                "id": "$g_paperwork_trans_table.all_selected.pet_paperwork_id#str.strip('[]').replace(' ', '')"
            },

            "on_start": ("$g_paperwork_trans_table.remove_row", "$g_paperwork_trans_table.all_selected"),
            "on_finish": "load_trans_paperworks"
        },

        #  Customer Transfers
        "get_layaways": {
            "method": "post",
            "resource": "/apps/cash_register/queries/read__qpt__open_transactions",

            "pull": {
                "qp_cust_entity_id": "$storage.id",
                "qp_txn_type": "Layaway",
                "order_by": "> [('{$g_layaways_table.sort_field}', '{$g_layaways_table.sort_order}')]",
                "columnsn": "transaction_id,txn_type_number,txn_opened_on,txn_subtotal," +
                            "customer_full_name,txn_grand_total,txnpay_amount_sum,txn_status"
            },

            "push": {
                "$g_layaways_table": "$result"
            },

            "on_finish": [
                ("if", "$result#length > 0",
                     "$g_salestab_tabWidget.show_tab('g_layaways_tab')",
                     "$g_salestab_tabWidget.hide_tab('g_layaways_tab')",)
            ]
        },

        "display_layaway": {
            "dialog": {
                "name": "InvoiceHistory",

                "data": {
                    "flags": ["display"],
                    "ids": "$g_layaways_table.all_selected.transaction_id"
                }
            },

            "on_finish": "load_brands"
        },

        "get_customer_pets": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__list_customer_pets",

            "pull": {
                "qp_cust_entity_id": "$storage.id",
                "order_by": "> [('{$g_pets_table.sort_field}', '{$g_pets_table.sort_order}')]",
                "columnsn": "customer_pet_id,custpet_pet_id,custpet_name,custpet_breed_name,ptype_name,"
                            "custpet_microchip_numbers_str,custpet_color_markings,custpet_identifiers,"
                            "custpet_gender,custpet_birthdate"
            },

            "push": {
                "$g_pets_table": "$result"
            }
        },

        "manage_customer_pet": {
            "dialog": {
                "name": "ManageCustomerPets",

                "data": {
                    "flags": ["update"],
                    "ids": "$g_pets_table.all_selected.customer_pet_id",
                }
            },

            "on_accept": [
                ("transfer", "get_customer_pets")
            ]
        },

        "create_customer_pet": {
            "dialog": {
                "name": "ManageCustPetNewDialog",

                "data": {
                    "flags": ["create"],
                    "entity_id": "$storage.id",
                }
            },

            "on_finish": [
                ("transfer", "get_customer_pets")
            ]
        },

        "delete_customer_pet": {
            "method": "post",
            "resource": "/apps/purchasing_and_receiving/queries/delete__tbl__customer_pets",

            "pull": {
                "qp_customer_pet_id": "$g_pets_table.all_selected.customer_pet_id"
            },

            "on_success": ("transfer", "get_customer_pets")
        },

        #  Tax Exemptions
        "get_taxexemptions": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt_customer_taxes",

            "pull": {
                "qp_cust_entity_id": "$storage.id",
                "qp_only_overrides": True,
                "order_by": "> [('{$g_tax_exemption_table.sort_field}', '{$g_tax_exemption_table.sort_order}')]",
            },

            "on_success": [
                ("if", "$result#length > 0",
                    [
                        "$g_tax_exemption_table.show",
                        ("push", "$g_tax_exemptions_label", "Customer has tax exemptions."),
                        ("push", "$g_tax_exemption_table", "$result")
                    ],
                    [
                        "$g_tax_exemption_table.hide",
                        ("push", "$g_tax_exemptions_label", "Customer has no tax exemptions."),
                        ("push", "$g_tax_exemption_table", [])
                    ])
            ],

            "on_failure": ("error_message", "An error occurred when reading the taxes!")
        },

        "update_taxcertificates": {
            "dialog": {
                "name": "ManageTaxExemptCertificates",

                "data": {
                    "flags": [],
                    "entity_id": "$storage.id"
                }
            },

            "on_finish": ("transfer", "get_taxexemptions")
        },

        #  House Accounts
        "get_houseaccounts": {
            "method": "post",
            "resource": "/apps/cash_register/queries/read__qpt__list_house_accounts",

            "pull": {
                "qp_cust_entity_id": "$storage.id",
                "qp_only_primary_customer": False,
                "qp_hacc_status": "Open,Store Credit",
                "order_by": "> [('{$g_houseaccount_table.sort_field}', '{$g_houseaccount_table.sort_order}')]",
            },

            "push": {
            },

            "on_success": [
                ("if", "$result#length > 0",
                    [
                        "$g_houseaccount_table.show",
                        "$g_newhouseaccount_button.hide",
                        "$g_payhouseaccount_button.show",
                        ("if", "$app.name == 'Cash Register' or  or $app.name = 'House Accounts'",
                            "$g_houseaccountcontrols_frame.show"),
                        ("if", "$app.permissions.has_role(-8,-9,-19)",
                            "$g_managehouseaccount_button.show"),

                        ("push", "$g_houseaccount_label", "Customer has House Accounts."),
                        ("push", "$g_houseaccount_table", "$result")
                    ],
                    [
                        "$g_houseaccount_table.hide",
                        "$g_houseaccountcontrols_frame.hide",
                        ("if", "($app.name == 'Cash Register' or $app.name = 'House Accounts') and $app.permissions.has_role(-8,-9)",
                            [
                                "$g_houseaccountcontrols_frame.show",
                                "$g_newhouseaccount_button.show",
                                "$g_managehouseaccount_button.hide",
                                "$g_payhouseaccount_button.hide"
                            ]),
                        ("push", "$g_houseaccount_label", "Customer has no House Accounts."),
                        ("push", "$g_houseaccount_table", [])
                    ])
            ],
        },

        "update_houseaccount": {
            "dialog": {
                "name": "ManageHouseAccount",
                "data": {
                    "flags": ["update", "register"],
                    "ids": "$g_houseaccount_table.all_selected.house_account_id"
                }
            },
            "on_finish": ("transfer", "get_houseaccounts")
        },

        "create_houseaccount": {
            "dialog": {
                "name": "CreateHouseAccountDialog",
                "data": {
                    "id": "$storage.id",
                    "business_name": "$g_business_lineedit.data",
                    "ent_name": "> {$g_entityfirst_lineedit.data} {$g_entitylast_lineedit.data}"
                }
            },
            "on_finish": ("transfer", "get_houseaccounts"),
        },

        #   History - Sales
        "get_product_sales_by_event": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__customer_movement",

            "pull": {
                "qp_entity_id": "$storage.id",
                "qp_txn_type": "$g_sales_type_combbox.selected.value",
                "_qp_date_range": "$sales_datetime_range.extended_range",

                "order_by": "> {$g_sales_table.sort_field} {$g_sales_table.sort_order}"
            },

            "push": {
                "$g_sales_table": "$result"
            },

        },

        "get_product_sales_summary": {
            "method": "post",
            "resource": "/apps/products/queries/read__qpt__customer_movement_summary",

            "pull": {
                "qp_entity_id": "$storage.id",
                "qp_txn_type": "$g_sales_type_combbox.selected.value",
                "_qp_date_range": "$sales_datetime_range.extended_range",

                "order_by": "> {$g_sales_table.sort_field} {$g_sales_table.sort_order}"
            },

            "push": {
                "$g_sales_table": "$result"
            }
        },

        "show_invoice": {
            "dialog": {
                "name": "InvoiceHistory",

                "data": {
                    "flags": ["invoice"],
                    "ids": "$g_sales_table.selected.transaction_id#list",
                    "invoice_type": "$g_sales_table.selected.txn_type#list"
                }
            },

            "on_finish": [
                ("push", "$storage.temp", "$dialog.output.close"),
                ("if", "$storage.temp == True",
                    "$form.close"),
            ]
        },

        #  Quotes
        "get_quotes": {
            "method": "post",
            "resource": "/apps/any/queries/read__qpt__open_transactions",

            "pull": {
                "qp_txn_expiration_date_is_null": False,
                "qp_txn_type": "'Sale'",
                "qp_txn_loc_entity_id": "$storage.location_id",

                "qp_cust_entity_id": "$storage.id",

                "order_by": "> [('{$g_quotes_table.sort_field}', '{$g_quotes_table.sort_order}')]",

                # "limit": "$quote_paginator.limit",
                # "offset": "$quote_paginator.offset",

                # "_$quote_unisearch.key": "$quote_unisearch.value",

                "columnsn": "transaction_id,txn_type_number,txn_opened_on,txn_subtotal,user_display_name," +
                            "customer_full_name,txn_grand_total,txn_reason"
            },

            "push": {
                "$g_quotes_table": "$result",
            },

            "on_finish": [
                ("if", "$result#length > 0",
                    "$g_salestab_tabWidget.show_tab('g_openorders_tab')",
                    "$g_salestab_tabWidget.hide_tab('g_openorders_tab')",)
            ]
        },

        #  Export to Excel
        "print_customer_product_sales_by_event": {
            "method": "post",
            "resource": "/apps/any/queries/rpt_customer_purchase_by_event",
            "output_format": "excel",

            "pull": {
                "qp_entity_id": "$storage.id",
                "qp_txn_type": "$g_sales_type_combbox.selected.value",
                "qp_date_range": "$sales_datetime_range.extended_range",
                "order_by": "> {$g_sales_table.sort_field} {$g_sales_table.sort_order}",
                "use_map": "True",
                "format": "excel",
                "columns": "txn_closed_date,txn_type,txn_type_number,user_display_name,txnline_product_id,"
                           "txnline_product_description,txnline_qty_sum,txnline_amount,txnline_discount,"
                           "net_price"
            },

            "on_success": [
                ("save_excel_export", "$result.document",
                 "> {$g_entityfirst_lineedit.data#replace_special}_{$g_entitylast_lineedit.data#replace_special}_purchase_by_event_{#datetime.strftime('%m%d%Y_%H%M%S')}")
            ],

            "on_failure": [
                ("if", "$result",
                 #  ("if", "$result#get('message') == ''",
                 #   ("info_message", ""),
                 ("error_message", "An error occurred when print the report!")),
            ]
        },

        "print_customer_product_sales_summary": {
            "method": "post",
            "resource": "/apps/any/queries/export_customer_purchase_summary",
            "output_format": "excel",

            "pull": {
                "qp_entity_id": "$storage.id",
                "qp_txn_type": "$g_sales_type_combbox.selected.value",
                "qp_date_range": "$sales_datetime_range.extended_range",
                "order_by": "> {$g_sales_table.sort_field} {$g_sales_table.sort_order}",
                "use_map": "True",
                "format": "excel",
                "columns": "period,txn_type,txnline_qty_sum,txnline_amount_sum,txnline_discount_sum,"
                           "net_price_sum"
            },

            "on_success": [
                ("save_excel_export", "$result.document",
                 "> {$g_entityfirst_lineedit}_{$g_entitylast_lineedit}_purchase_summary_{#datetime.strftime('%m%d%Y_%H%M%S')}")
            ],

            "on_failure": [
                ("if", "$result",
                    ("error_message", "An error occurred when print the report!")),
            ]
        },

        #  Manufacturer Transfers
        "get_brands": {
            "method": "post",
            "resource": "/apps/purchasing_and_receiving/queries/read__tbl__mfg_brands",

            "pull": {
                "qp_mfgbr_entity_id": "$storage.id",

                "order_by": "> [('{$g_brands_table.sort_field}', '{$g_brands_table.sort_order}')]",
                "columnsn": "mfg_brand_id,mfgbr_brand_name,mfgbr_entity_id"
            },

            "push": {
                "$g_brands_table": "$result"
            }
        },

        "create_brand": {
            "dialog": {
                "name": "ManageBrand",

                "data": {
                    "flags": ["create"],
                    "ids": "$storage.id"
                }
            },

            "on_accept": [
               ("push", "$storage.brand_name", "$dialog.output.brand_name"),
               "add_brand_to_table"
            ],

            "on_finish": [
            ]
        },

        "manage_brand": {
            "dialog": {
                "name": "ManageBrand",

                "data": {
                    "flags": ["update"],
                    "ids": "$g_brands_table.all_selected.mfg_brand_id",
                    "brands": "$g_brands_table.all_selected"
                }
            },

            "on_accept": [
                ("push", "$storage.brand_names", "$dialog.output.brands"),
                "update_brand_in_table"
            ]
        },

        "delete_brand": {
            "method": "post",
            "resource": "/apps/purchasing_and_receiving/queries/delete__tbl__mfg_brands",

            "pull": {
                "qp_mfg_brand_id": "$g_brands_table.all_selected#reject('mfg_brand_id', None).mfg_brand_id"
            },

            "on_start": ("$g_brands_table.remove_rows", "$g_brands_table.all_selected")
        },

        #  Print Labels
        "get_address_label": {
            "resource": "/apps/any/labels/{id}",
            "method": "get",
            "query": {"id": -3},
            "on_success": [("push", "$storage.address_label_report_template_id", "$result")]
        },

        "get_card_label": {
            "resource": "/apps/any/labels/{id}",
            "method": "get",
            "query": {"id": -4},
            "on_success": [("push", "$storage.address_label_report_template_id", "$result")]
        },

        "get_address_label_report_template": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__tbl__vw_files_with_in_band_contents",
            "convert": "$result[0]",
            "pull": {"qp_file_id": "$storage.address_label_report_template_id.lbl_file_id"}
        },

        "get_address_report_data": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/rpt_entity_labels",
            "pull": {"qp_entity_id": "$storage.report_data"}
        },

        #  Custom Fields
        "get_custom_fields": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__pbreeder_cust_fields",

            "pull": {
                "qp_pbreeder_entity_id": "$storage.id"
            },

            "push": {
                "$g_customfield_table": "$result"
            },
        },

        "update_customfield": {
            "dialog": {
                "name": "ManageCustomFieldsEdit",

                "data": {
                    "flags": ["update", "breeders"],
                    "table": "pets",
                    "key": "$g_customfield_table.all_selected.pbrd_cust_field_key",
                    "value": "$g_customfield_table.all_selected.pbrd_cust_field_value",
                    "cusfld_type": "$g_customfield_table.all_selected.cusfld_type",
                    "cusfld_default": "$g_customfield_table.all_selected.cusfld_default",
                    "pbreeder_entity_id": "$storage.id"
                }
            },

            "on_finish": ("transfer", "get_custom_fields")
        },

        "add_custom_field": {
            "dialog": {
                "name": "ManageCustomFieldsEdit",

                "data": {
                    "flags": ["add", "breeders"],
                    "table": "breeders",
                    "pbreeder_entity_id": "$storage.id"
                }
            },

            "on_finish": ("transfer", "get_custom_fields")
        },

        "delete_custom_field": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/delete__qpt__pbreeder_cust_fields",

            "pull": {
                "qp_pbreeder_entity_id": "$storage.id",
                "qp_pbreeder_cust_field_key": "$g_customfield_table.all_selected.pbreeder_cust_field_key"
            },

            "on_finish": ("transfer", "get_custom_fields"),

            "on_failure": ("error_message", "An error occurred when deleting the record!")
        },

        #  Media
        "get_iamge_ratio": {
            "method": "post",
            "resource": "/apps/any/queries/read__tbl__settings",

            "convert": "$result[0]",

            "pull": {
                "qp_stn_name": "image_ratio",
            },

            "on_success": [
                ("if", "$result.stn_value  == '4x3'", "$cropper.set_ratio(1.333)"),  # ratio 1.333
                ("if", "$result.stn_value  == '16x9'", "$cropper.set_ratio(1.777)")  # ratio 1.777
            ]
        },

        "get_images": {
            "method": "get",
            "resource": "/api/pet_breeders/{pet_breeder_id}/media",

            "query": {
                "pet_breeder_id": "$storage.id",
            },

            "push": {
                "$media_viewer": "$result",
                "$storage.media": "$result",
                "$storage.ids": "$result#reject('type', 'video').id"
            },

            "on_success": [
                ("transfer", "get_data_for_images")
            ]
        },

        "get_data_for_images": {
            "type": "iterator",

            "iterator": "$storage.ids",
            "transfer": "get_data_for_image",

            "on_iter_success": [
                ("if", "$result", "$media_viewer.update($result)")
            ]
        },

        "get_data_for_image": {
            "method": "get",
            "resource": "/api/pet_breeders/{pet_breeder_id}/images/{image_id}",

            "cache": ("app_cache", "> breed_image_{$iter}", {"days": 1}),

            "query": {
                "pet_breeder_id": "$storage.id",
                "image_id": "$iter",
            },

            "pull": {
                "include_contents": True
            }
        },

        "create_image": {
            "method": "post",
            "resource": "/api/pet_breeders/{pet_breeder_id}/images",

            "query": {
                "pet_breeder_id": "$storage.id",
            },

            "pull": {
                "description": "$g_description_lineedit or None",
                "filedata": "$cropper.data",
                "filename": "$storage.filename"
            },

            "on_success": [
                "$g_cropper_area.hide",
                "$g_crop_and_save_btn.hide",
                "$g_cropper_cancel_btn.hide",
                "$g_addedit_groupBox.show",
                ("push", "$g_uploadfilename_lineedit", ""),
                ("transfer", "get_images")
            ],

            "on_failure": ("error_message", "An error occurred when uploading the file"),

            "on_finish": [
                "$g_cropper_area.hide",
                "$g_crop_and_save_btn.hide",
                "$g_cropper_cancel_btn.hide",

                "$g_addedit_groupBox.show",

                ("push", "$g_description_lineedit", ""),
                ("push", "$g_title_lineedit", ""),
                ("push", "$g_tags_lineedit", "")
            ]
        },

        "remove_image": {
            "method": "delete",
            "resource": "/api/pet_breeders/{pet_breeder_id}/images/{image_id}",

            "query": {
                "pet_breeder_id": "$storage.id",
                "image_id": "$item.id",
            },

            "on_success": "$media_viewer.remove($result)"
        },

        "remove_video": {
            "method": "delete",
            "resource": "/api/pet_breeders/{pet_breeder_id}/videos/{image_id}",

            "query": {
                "pet_breeder_id": "$storage.id",
                "image_id": "$item.id",
            },

            "on_success": "$media_viewer.remove($result)"
        },

        "change_image_title": {
            "method": "put",
            "resource": "/api/pet_breeders/{pet_breeder_id}/images/{image_id}",

            "query": {
                "pet_breeder_id": "$storage.id",
                "image_id": "$storage.media_id"
            },

            "pull": {
                "description": "$g_description_lineedit or None",
                "tags": "$g_tags_lineedit or None",
            },

            "on_success": [
                ("push", "$storage.cache", "> breed_image_{$storage.media_id}"),
                "$app.cache.delete($storage.cache)",
                ("transfer", "get_images")
            ],

            "on_finish": [
                "$g_cropper_area.hide",
                "$g_crop_and_save_btn.hide",
                "$g_cropper_cancel_btn.hide",

                "$g_addedit_groupBox.show",

                ("push", "$g_description_lineedit", ""),
                ("push", "$g_title_lineedit", ""),
                ("push", "$g_tags_lineedit", "")
            ]
        },

        "change_video_title": {
            "method": "put",
            "resource": "/api/pet_breeders/{pet_breeder_id}/videos/{image_id}",

            "query": {
                "pet_breeder_id": "$storage.id",
                "image_id": "$storage.media_id"
            },

            "pull": {
                "description": "$g_description_lineedit or None",
                "tags": "$g_tags_lineedit or None",
                "title": "$g_title_lineedit or None"
            },

            "on_success": [
                ("transfer", "get_images")
            ],

            "on_finish": [
                "$g_cropper_area.hide",
                "$g_crop_and_save_btn.hide",
                "$g_cropper_cancel_btn.hide",

                "$g_addedit_groupBox.show",

                ("push", "$g_description_lineedit", ""),
                ("push", "$g_title_lineedit", ""),
                ("push", "$g_tags_lineedit", "")
            ]
        },

        "update_media_order": {
            "method": "put",
            "resource": "/api/pet_breeders/{pet_breeder_id}/media",

            "query": {
                "pet_breeder_id": "$storage.id",
            },

            "pull": {
                "sort_order": "$media_viewer.sort_order"
            },
        },

        "create_video": {
            "method": "post",
            "resource": "/api/pet_breeders/{pet_breeder_id}/videos",

            "query": {
                "pet_breeder_id": "$storage.id",
            },

            "pull": {
                "filename": "$file_uploader.file",
                "description": "$file_uploader.file",
                "title": "",
                "filedata": "$file_uploader.data",
                "tags": ""
            },

            "on_finish": [
                ("transfer", "get_images"),
                ("push", "$g_description_lineedit", ""),
                ("push", "$g_title_lineedit", ""),
                ("push", "$g_tags_lineedit", "")
            ]
        },

        # Repeat Delivery
        "read_repeat_deliveries": {
            "method": "post",
            "resource": "/apps/pet_tracker/queries/read__qpt__list_customer_recurring_sales",

            "pull": {
                "qp_cust_entity_id": "$storage.id",
                "order_by": "> [('{$g_repeatdelivery_table.sort_field}', '{$g_repeatdelivery_table.sort_order}')]",
                "qp_include_frequency_text": True,
                "qp_only_base_record": True,
                "qp_custrs_status": "Pending",
                # "qp_loc_entity_id": "$g_location_repeat_orders_combobox.selected.loc_entity_id"
            },

            "push": {
                "$g_repeatdelivery_table": "$result",
            }
        },

        "select_repeat_delivery_product": {
            "dialog": {
                "name": "SelectProductSearchDialog",

                "data": {
                    "flags": ["select"],
                }
            },

            "on_accept": [
                {"if": {
                    "condition": "$dialog.output.product_data",
                    "then": [
                        ("push", "$storage.select_delivery_product", "$dialog.output.product_data"),
                        ("transfer", "create_repeatdeliveries"),
                    ],
                }}
            ],
        },

        "create_repeatdeliveries": {
            "dialog": {
                "name": "ManageProductDeliveryScheduleDialog",

                "data": {
                    "flags": ["create"],
                    "product": "$storage.select_delivery_product",
                    "location_id": "$storage.location_id",
                    "entity_id": "$storage.id",
                    "customer": "$storage.entity_info"
                }
            },
            "on_accept": [
                "$repeat_orders_timer.start_timer",
                ("push", "$storage.repeat_order_task_id", "$dialog.output.results.task_id"),
                "$g_repeat_order_status_label.show",
                ("transfer", "get_repeat_order_status"),
            ],

        },

        "update_repeatdeliveries": {
            "dialog": {
                "name": "ManageProductDeliveryScheduleDialog",

                "data": {
                    "flags": ["update"],
                    "data": "$read_repeat_deliveries.all_selected",
                    "ids": "$g_repeatdelivery_table.all_selected.custrs_parent_customer_recurring_sale_id",
                    "entity_id": "$storage.id",
                    "customer": "$storage.entity_info"
                }
            },

            "on_finish": ("transfer", "read_repeat_deliveries")
        },

        "delete_repeatdeliveries": {
            "method": "delete",
            "resource": "/api/recurring_sales",
            "content_type": "json",

            "pull": {
                "ids": "$g_repeatdelivery_table.all_selected.custrs_parent_customer_recurring_sale_id"
            },

            "on_success": ("transfer", "read_repeat_deliveries")
        },

        "delete_nextdelivery": {
            "method": "delete",
            "resource": "/api/recurring_sales/{id}",
            "content_type": "json",

            "query": {
                "id": "$storage.nextdelivery[0].id"
            },

            "on_success": ("transfer", "get_order")
        },

        "get_repeat_order_status": {
            "method": "post",
            "resource": "/apps/products/queries/read__tbl__tasks",

            "pull": {
                "qp_task_id": "$storage.repeat_order_task_id",
            },

            "push": {
                "$storage.results": "$result[0]",
            },

            "on_success": [
                ("if", "$result.task_status[0] == 'Success'",
                    [
                        "$repeat_orders_timer.start_timer",
                        ("push", "$storage.repeat_order_task_id", None),
                        ("transfer", "read_repeat_deliveries"),
                        "$g_repeat_order_status_label.hide"
                    ]),
                ("if", "$result.task_status[0] == 'Failed'",
                    [
                        "$repeat_orders_timer.stop_timer",
                        ("push", "$storage.repeat_order_task_id", None),
                        ("transfer", "read_repeat_deliveries"),
                        "$g_repeat_order_status_label.hide"
                    ])
            ],

            "on_failure": [
                "$repeat_orders_timer.stop_timer",
                "$g_repeat_order_status_label.hide",
                ("error_message", "'\n'.join($errors)"),
            ]
        },

        # -- Mass Action
        "massaction_repeatdeliveries": {
            "dialog": {
                "name": "SelectMassActionDialog",

                "data": {
                    "flags": ["repeatdeliveries"],
                    "ids": "$g_repeatdelivery_table.all_selected.customer_recurring_sale_id",
                    "next_dates": "$g_repeatdelivery_table.all_selected.custrs_next_order",
                }
            },

            "on_finish": ("transfer", "read_repeat_deliveries")
        },

        # webstie
        "read_marketing_subscriptions": {
            "method": "get",
            "resource": "/api/marketing_lists",

            "pull": {
                "is_enabled": True,
            },

            "push": {
                "$storage.subscriptions": "$result"
            },

            "on_failure": [

            ]
        },

        "read_entity_marketing_subscription": {
            "method": "get",
            "resource": "/api/entities/{id}",

            "query": {
                "id": "$storage.id",
            },

            "pull": {
                "fields_include": ["customer.marketing_subscriptions"]
            },

            "on_success": [
                ("push", "$storage.entity_subscription_ids", "$result#get('customer')#get('marketing_subscriptions')"),
                ("push", "$g_marketingsubsciptions_table", "$storage.subscriptions"),
            ],

            "on_failure": [
            ]
        },

        "update_entity_marketing_subscription": {
            "method": "put",
            "resource": "/api/entities/{id}",

            "query": {
                "id": "$storage.id",
            },

            "pull": {
                "customer": {
                    "marketing_subscriptions": [
                        # {   "operation": "Subscribe",
                        #     "marketing_list_id": marketing_list.id
                        # }
                        # use "id" to update or delete existing
                    ],
                },
            },

            "on_success": [
                ("push", "$storage.entity_subscription_ids", "$result#get('customer')#get('marketing_subscriptions')"),
                ("push", "$g_marketingsubsciptions_table", "$storage.subscriptions"),
            ],

            "on_failure": [
            ]
        },

        "get_website_integrations": {
            "method": "post",
            "resource": "/apps/settings_and_configuration/queries/read__qpt__integration_settings",

            "pull": {
                "order_by": "> [('{$g_integrations_table.sort_field}', '{$g_integrations_table.sort_order}')]",
                "qp_integration_id": 7,
            },

            "on_success": [
                ("push", "$storage.websites", "$result"),
            ]
        },

        "save_entity_email": {
            "method": "put",
            "resource": "/apps/any/entities/{id}",

            "query": {
                "id": "$storage.id",
            },

            "on_start": ["disable_buttons", "pull_entity_ids", "pull_entity", "pull_entity_subtype"],
            "on_finish": ("if", "$storage.status[1] == 'location'", "save_receipt_content"),

            "on_failure": [
                ("change_state", "grp_command_buttons", {"default_state": "enabled"}),
            ]
        },

        "create_username": {
            "method": "put",
            "resource": "/api/entities/{id}",
            "content_type": "json",

            "query": {
                "id": "$storage.id",
            },

            "pull": {
                "user": {
                    "username": "$storage.new_emails[1]",
                    "display_name": "$storage.new_emails[1]",
                },
                "entity_roles": ("#map", "$storage.entity_roles", {"role_id": "$_"})
            },

            "on_success": [
                ("push", "$g_webusername_lineedit.data", "$storage.new_emails[1]"),
                ("if", "$storage.websites#length > 1",
                    ("transfer", "reset_webpassword_dialog"),
                    ("transfer", "reset_webpassword")),
                ("info_message", "A username based on the users email has been created and a reset password email has been sent to the user."),
                "$g_createusername_button.hide",
                "$g_resetpassword_button.show",

            ],

            "on_failure": [
            ]
        },

        # Used only if multiple websites are found
        "reset_webpassword_dialog": {
            "dialog": {
                "name": "SelectWebsiteDialog",

                "data": {
                    "username": "$g_webusername_lineedit.data",
                }
            },
        },

        # Used only if one website is found
        "reset_webpassword": {
            "method": "post",
            "resource": "/api/password_restore",

            "pull": {
                "username": "$g_webusername_lineedit.data",
                "integration_setting_id": "$storage.websites[0].integration_setting_id"

            },

            "on_success": [
                ("info_message", "Password has been reset.")
            ]
        },
    },

    "fpe_custom_widgets": [
        {
            "type": "paginator",
            "name": "paginator",

            "left_arrow": "g_previouspage_label",
            "right_arrow": "g_nextpage_label",
            "page_input": "g_currentpage_lineedit",
            "total_label": "g_totalpages_label",

            "initial_page": 1,
            "per_page": 1,

            "on_change": ("transfer", "save_existing_entity"),

            "on_changed": [
                "prepare_form_subtype",
                "load_basic_data"
            ]
        },

        {
            "type": "datetime_range",
            "name": "sales_datetime_range",

            "start": "g_sales_startdate_timeedit",
            "end": "g_sales_enddate_timeedit"
        },

        {
            "type": "progress_bar",
            "name": "progress_bar",

            "frame": "g_progress_frame",
            "completed": "g_completed_frame"
        },

        {   # Breeder Paperwork
            "type": "paginator",
            "name": "g_paperworks_breeder_paginator",

            "left_arrow": "g_previouspage_breeder_label",
            "right_arrow": "g_nextpage_breeder_label",
            "page_input": "g_currentpage_breeder_lineedit",
            "total_label": "g_totalpages_breeder_label",

            "initial_page": 1,
            "per_page": 25
        },

        {   # Broker Paperwork
            "type": "paginator",
            "name": "g_paperworks_broker_paginator",

            "left_arrow": "g_previouspage_broker_label",
            "right_arrow": "g_nextpage_broker_label",
            "page_input": "g_currentpage_broker_lineedit",
            "total_label": "g_totalpages_broker_label",

            "initial_page": 1,
            "per_page": 25
        },

        {    # Transporter Paperwork
            "type": "paginator",
            "name": "g_paperworks_trans_paginator",

            "left_arrow": "g_previouspage_trans_label",
            "right_arrow": "g_nextpage_trans_label",
            "page_input": "g_currentpage_trans_lineedit",
            "total_label": "g_totalpages_trans_label",

            "initial_page": 1,
            "per_page": 25
        },

        {
            "type": "check_combobox",
            "name": "c_group_checkcombobox",
            "widget": "g_group_checkcombobox",
        },

        {
            "type": "droplist",
            "name": "phone_droplist",

            "container": "g_phone_listWidget",

            "add": ":/entity_dialog/add",
            "delete": ":/entity_dialog/delete",
            "delete_hide": True,
            "sort": ":/entity_dialog/sort",

            "required": True,

            "on_change": [
                ("if", "$phone_droplist.last_line_update#length > 7",
                 ("transfer", "duplicate_entity_numbers")),
            ],

            "default": {"cnum_number": None, "numt_contact_number_type": "Mobile",
                        "contact_number_id": "@null", "phone_id": PhoneNumberType.MOBILE},

            "layout": [
                {"field": "cnum_number",
                 "data": {"type": "lineedit", "label": "Phone", "label_width": 61, "name": "g_phone_lineedit",
                          "": "", "width": 110}},
                {"field": "numt_contact_number_type",
                 "data": {"type": "combobox", "label": "Type", "update": "only", "name": "g_phonetype_combobox"}}
            ]
        },

        {
            "type": "droplist",
            "name": "email_droplist",

            "container": "g_email_listWidget",

            "add": ":/entity_dialog/add",
            "delete": ":/entity_dialog/delete",
            "delete_hide": True,
            "sort": ":/entity_dialog/sort",

            "required": True,

            "default": {"eml_email": None, "eml_email_type_id": -1, "email_id": None, "emlt_email_type": "Home"},

            "on_change": [
                 ("transfer", "duplicate_email"),
            ],

            "layout": [
                {"field": "eml_email",
                 "data": {"type": "lineedit", "label": "Email", "label_width": 57, "update": "only",
                          "name": "g_email_lineedit", "": ""}}
            ]
        },

        # Media
        {
            "type": "media_viewer_list",
            "name": "media_viewer",

            "list": "g_entity_images_list",

            "key": "id",
            "key2": "id",
            "data": "filedata",
            "description": "description",
            "position": "sort_order",
            "tags": "tags",
            "title": "title",

            "placeholder": "Picture Description",

            "on_remove_image": ("transfer", "remove_image"),

            "on_remove_video": ("transfer", "remove_video"),

            "on_edit": [
                ("push", "$storage.type", "$item.type",),
                ("if", "$item.type == 'image'",
                 [
                     "$g_mediatitle_frame.hide",

                     "$g_cropper_area.show",
                     "$g_crop_and_save_btn.show",
                     "$g_cropper_cancel_btn.show",
                     "$g_size_label.show",
                     "$g_addedit_groupBox.hide",

                     "$storage.filename = $item.filename",
                     "$cropper.updated = False",
                     "$cropper.data = $item.filedata",

                     "$cropper.crop_updated(False)",
                 ],
                 [
                     "$g_cropper_area.show",
                     "$g_crop_and_save_btn.show",
                     "$g_cropper_cancel_btn.show",
                     "$g_size_label.hide",
                     "$g_addedit_groupBox.hide",
                     "$g_mediatitle_frame.show",

                     "$cropper.crop_updated(False)",
                     "$cropper.data = None",
                     ("push", "$g_title_lineedit",
                      "($item.video.meta.title if $item.video.meta.title != '@null') or ''"),
                     ("push", "$g_tags_lineedit", "(','.join($item.tags) if ','.join($item.tags) != '@null') or ''")
                 ]),

                "$storage.media_id = $item.id",
                ("push", "$g_description_lineedit", "($item.description if $item.description != '@null') or ''"),
            ],

            "on_image_description_changed": [
                ("transfer", "change_image_title"),
            ],

            "on_video_description_changed": [
                ("transfer", "change_video_title"),
            ],

            "on_sort": [
                ("transfer", "update_media_order"),
            ],

            "on_click": "$app.weburl($media_viewer.link)"
        },

        {
            "type": "file_uploader",
            "name": "file_uploader",

            "mask": "Images (*.jpg *.jpeg *.mp4 *.mov *.mpeg4 *.avi *.wmv *.mpegps *.flv *.3gpp *.png *.PNG " +
                    "*.JPG *.JPEG *.MP4 *.MOV *.MPEG4 *.AIV *.WMV *.MPEGPS *.FLV *.3GPP *.webp *.jfif)",
            "title": "Select image",

            "browse": "g_selectfile_button",
            "display": "g_uploadfilename_lineedit",

            "on_upload": [
                ("if", "'.jpg' in $file_uploader.file.lower() or"
                       " '.jpeg' in $file_uploader.file.lower() or"
                       " '.jfif' in $file_uploader.file.lower() or"
                       " '.webp' in $file_uploader.file.lower() or"
                       " '.png' in $file_uploader.file.lower()",

                 [
                     "$g_mediatitle_frame.hide",
                     ("push", "$storage.type", "image"),
                     "$g_cropper_area.show",
                     "$g_cropper_cancel_btn.show",
                     "$g_crop_and_save_btn.show",
                     "$cropper.crop_updated(True)",
                     "$g_addedit_groupBox.hide",
                     "$g_size_label.show",

                     "$storage.filename = $file_uploader.file",
                     "$cropper.data = $file_uploader.data"
                 ],
                 [
                     "$g_mediatitle_frame.show",
                     ("push", "$storage.type", "video"),
                     ("transfer", "create_video")
                 ]),
            ]
        },

        {
            "type": "cropper",
            "name": "cropper",

            "frame": "g_cropper_frame",
            "size_label": "g_size_label",

            "ratio": 1,
            # "ratio": 1.333,  #  size 4x3
            # "ratio": 1.777,  #  size 16x9

            "width": 600,
            "height": 300,

            "return_width": 800,
            "return_height": 800
        },

        {
            "type": "returninvoices_popup",
            "name": "returninvoices_popup"
        },

        # Repeat Delivery
        {
            "type": "datetime_range",
            "name": "datetime_repeatdelivery_range",

            "start": "g_start_repeatdelivery_datetimeedit",
            "end": "g_end_start_repeatdelivery_datetimeedit",

            "on_change": [
                ("if", "$storage.loading == False",
                    ("transfer", "read_repeat_deliveries"))
            ]
        },

        {
            "type": "simple_timer",
            "name": "repeat_orders_timer",

            "minutes": .1,

            "refresh": ("transfer", "get_repeat_order_status")
        },
    ],

    "fpe_validations": {
        "g_addressline1_lineedit": {
            "rule": {"type": "string", "min": 3, "max": 50},
            "message": "Contact Info Tab: Address length must be between 3 and 50 characters."
        },

        "g_city_lineedit": {
            "rule": {"type": "string", "min": 3, "max": 50},
            "message": "Contact Info Tab: City length must be between 3 and 50 characters."
        },

        "g_zip_lineedit_length": {
            "rule": "not $g_zip_lineedit",
            "message": "Contact Info Tab: Zip Codes required."
        },

        # Only require ZIP for Canada, Mexico and USA
        # "g_zip_lineedit": {
        #     "rule": "not (($g_zip_lineedit.data#length <= 0) and "+
        #             "     ($g_country_combobox.selected.country_id in #list(38,142,231)))", # 38 CAN, 142 MEX, 231 USA
        #     "message": "Contact Info Tab: Zip Codes must be 5 to 9 characters."
        # },

        "g_zip_us_lineedit": {
            "rule": "($g_zip_lineedit.data.replace(' ', '').replace('-', '')#length == 5) or ($g_zip_lineedit.data.replace(' ', '').replace('-', '')#length == 9)",  # 231 USA
            "message": "Contact Info Tab: Zip Codes must be 5 or 9 characters."
        },

        "g_zip_us_lineedit": {
            "rule": "($g_zip_lineedit.data.replace(' ', '').replace('-', '')#length == 5) or ($g_zip_lineedit.data.replace(' ', '').replace('-', '')#length == 9)",
            # 231 USA
            "message": "Contact Info Tab: Zip Codes must be 5 or 9 characters."
        },

        "g_zip_can_lineedit": {
            "rule": "$g_zip_lineedit.data.replace(' ', '')#length == 6",  # 38 CAN
            "message": "Contact Info Tab: Zip Codes must be 6 characters."
        },

        "g_zip_mex_lineedit": {
            "rule": "$g_zip_lineedit.data.replace(' ', '')#length == 5",  # 142 MEX
            "message": "Contact Info Tab: Zip Codes must be 5 characters."
        },

        # "g_employee_id_lineedit": {
        #     "rule": {"type": "string", "min": 0, "max": 16},
        #     "message": "Employee Id length must be between 1 and 16 characters."
        # },

        "g_usda_lineedit": {
            "rule": "$value#is_usda or ($value.lower() == 'exempt') or ($value.lower() == 'hobby') or ($value == '')",
            "message": "Breeder Info Tab: USDA # format is incorrect. If there is no USDA #, use 'Exempt' or 'Hobby' as a placeholder in that field."
        },

        "g_usda_lineedit_val": [
          "g_usda_lineedit"
        ],

        "g_state_lineedit": {
            "rule": {"type": "string", "min": 0, "max": 10},
            "message": "Breeder Info Tab: State # name must have a maximum of 10."
        },

        "g_broker_usda_lineedit": {
            "rule": "$value#is_usda or ($value.lower() == 'exempt') or ($value.lower() == 'hobby') or ($value == '')",
            "message": "Broker Info Tab: USDA # format is incorrect."
        },

        "g_usda_lineedit_broker_val": [
          "g_broker_usda_lineedit"
        ],

        "g_broker_state_lineedit": {
            "rule": {"type": "string", "min": 0, "max": 10},
            "message": "Broker Info Tab: State # name must have a maximum of 10."
        },

        "g_transporter_usda_lineedit": {
            "rule": "$value#is_usda or ($value.lower() == 'exempt') or ($value.lower() == 'hobby') or ($value == '')",
            "message": "Transporter Info Tab: USDA # format is incorrect."
        },

        "g_usda_lineedit_transporter_val": [
          "g_transporter_usda_lineedit"
        ],

        "g_transporter_state_lineedit": {
            "rule": {"type": "string", "min": 0, "max": 10},
            "message": "Transporter Info Tab: State # name must have a maximum of 10."
        },

        "g_entityfirst_lineedit": {
            # "rule": {"type": "string", "min": 1, "max": 50},
            "rule": "($g_entityfirst_lineedit.data#length > 0 and $g_entityfirst_lineedit.data#length <= 50) or "
                    "($g_business_lineedit.data#length > 0 and $g_business_lineedit.data#length <= 50)",
            "message": "First Name length must be between 1 and 50 characters."
        },

        "g_entitylast_lineedit": {
            # "rule": {"type": "string", "min": 1, "max": 50},
            "rule": "($g_entitylast_lineedit.data#length > 0 and $g_entitylast_lineedit.data#length <= 50) or "
                    "($g_business_lineedit.data#length > 0 and $g_business_lineedit.data#length <= 50)",
            "message": "Last Name length must be between 1 and 50 characters."
        },

        "g_displayname_lineedit": {
            "rule": {"type": "string", "min": 3, "max": 50},
            "message": "Employee Settings Tab: Employee Display Name length must be between 3 and 50 characters."
        },

        "g_password_lineedit": {
            "rule": "$g_password_lineedit.data#length > 2 and $g_password_lineedit.data#length < 51",
            # "rule": {"type": "string", "min": 3, "max": 50},
            "message": "Employee Settings Tab: Employee Password length must be between 3 and 50 characters."
        },

        "appliactions_custom_validation": {
            "rule": "$g_moduleaccess_list.selected",
            "message": "Employee Settings Tab: You must select at least one Application in the App Access list."
        },

        "locations_custom_validation": {
            "rule": "$g_locationaccess_list.selected or $g_locationaccess_list.readonly_items != None",
            "message": "Employee Settings Tab: You must select at least one Location in the Location Access list."
        },

        "phone_custom_validation": {
            "rule": "$g_homephone_lineedit or $g_mobilephone_lineedit or $g_officephone_lineedit or $g_fax_lineedit",
            "message": "Contact Info Tab: At least one phone number is required."
        },

        "emailtype_custom_validation": {
            "rule": "($g_emailtype_combobox.is_selected and $g_email_lineedit) or " +
                    "((not $g_emailtype_combobox.is_selected) and (not $g_email_lineedit))",
            "message": "Contact Info Tab: Email and Email Type are required when entering email info."
        },

#$g_supplierinfo_table.data#get('send_po_method', None)
        "possible_phone_validation": {
            "rule": "$phone_droplist.data.cnum_number#remove_from_list(None)#remove_from_list('')#is_possible_number == True",
            "message": "Contact Info Tab: Invalid phone number."
        },

        "g_leadtime_lineedit": {
            "value": "$g_leadtime_lineedit",
            "rule": "$value#length > 0 and $value#float > 0",
            "message": "Supplier Info Tab: Lead Time is required."
        },

        "g_orderfrequency_lineedit": {
            "value": "$g_orderfrequency_lineedit",
            "rule": "$value#length > 0 and $value#float > 6",
            "message": "Supplier Info Tab: Order Frequency 7 or greater is required."
        },

        "g_minorderamount_lineedit": {
            "value": "$g_minorderamount_lineedit",
            "rule": "$value#length > 0",
            "message": "Supplier Info Tab: A Minimum Order Amount is required."
        },

        "sendviaemail_custom_validation": {
            "rule": "($g_supplierinfosendvia_combobox.selected.value == 'Email' and ($email_droplist.data.eml_email[0] != None "
                    "and $email_droplist.data.eml_email[0] != '')) or "
                    "($g_supplierinfosendvia_combobox.selected.value != 'Email') or"
                    " ($email_droplist.data.eml_email[0] != None and $email_droplist.data.eml_email[0] != '')",
            "message": "Contact Info Tab: A valid email is required for the Supplier to set a Supplier to send Purchase Orders via Email."
        },

        "sendviaemail_custom_validation_corp": {
            "rule": "(not 'Email' in $g_supplierinfo_table.data#get('send_po_method') and ($email_droplist.data.eml_email[0] != None "
                    "and $email_droplist.data.eml_email[0] != '')) or "
                    "(not 'Email' in $g_supplierinfo_table.data#get('send_po_method')) or"
                    " ($email_droplist.data.eml_email[0] != None and $email_droplist.data.eml_email[0] != '')",
            "message": "Contact Info Tab: A valid email is required for the Supplier to set a Supplier to send Purchase Orders via Email."
        },

        "g_employeerole_combobox": {
            "value": "$widget.is_selected",
            "rule": "$value == True",
            "message": "Employee Role is required"
        },

        "address_other_validations": ["g_addressline1_lineedit", "g_city_lineedit", "g_zip_lineedit"],
        "address_us_validations": ["g_addressline1_lineedit", "g_city_lineedit", "g_zip_us_lineedit"],
        "address_can_validations": ["g_addressline1_lineedit", "g_city_lineedit", "g_zip_can_lineedit"],
        "address_mex_validations": ["g_addressline1_lineedit", "g_city_lineedit", "g_zip_mex_lineedit"],

        "email_validations": ["emailtype_custom_validation"],
        "employee_validations_create": ["g_entityfirst_lineedit", "g_entitylast_lineedit", "g_displayname_lineedit", "g_employeerole_combobox",
                                 # "appliactions_custom_validation", "locations_custom_validation"],
                                 "g_password_lineedit", "appliactions_custom_validation", "locations_custom_validation"],
        "employee_validations": ["g_entityfirst_lineedit", "g_entitylast_lineedit", "g_displayname_lineedit",
                                 "g_employeerole_combobox", "appliactions_custom_validation",
                                 "locations_custom_validation"],
        "supplier_validations": ["g_business_lineedit", "g_leadtime_lineedit", "g_orderfrequency_lineedit",
                                 "sendviaemail_custom_validation_corp", "g_minorderamount_lineedit"],
        "supplier_validations_non_corp": ["g_business_lineedit", "sendviaemail_custom_validation"],
        "manufacturer_validations": ["g_business_lineedit"],
        "breeder_validations": ["g_usda_lineedit", "g_state_lineedit"],
        "broker_validations": ["g_broker_usda_lineedit", "g_broker_state_lineedit"],
        "vet_validations": [],
        "transporter_validations": ["g_transporter_usda_lineedit", "g_transporter_state_lineedit"],
        "customer_validations": ["g_entityfirst_lineedit", "g_entitylast_lineedit"],
        "phone_validations": ["possible_phone_validation"],
    },

    "fpe_groups": {
        "app_lite_hide": [
            "g_label_group",
            "g_loyalty_group",
            "g_houseaccount_group",
            "g_savedcards_table",
            "g_savedpayments_controls_frame",
            "g_astro_link_frame",
            "g_astroid_frame",
            # "g_astro_tabwidget",
            "g_pets_table",
            "g_pets_buttons_frame",
            "g_frequentbuyer_tabwidget",
            "g_emergencycontact_table",
            "g_emergencycontact_buttons_frame",
            "g_website_ontrol_frame",
            "g_marketingsubsciptions_table",
            "g_website_username_frame",
            "g_address_table",
            "g_address_buttons_frame",
            "g_website_v_spacer_frame",
        ],

        "app_lite_show": [
            "g_address_not_available_frame",
            "g_websites_not_available_frame",
            "g_emer_contacts_not_available_frame",
            "g_frequentbuyer_not_available_frame",
            "g_pets_not_available_frame",
            "g_savedpayments_not_available_frame",
        ],

        "app_lite_disable": [

        ],

        "grp_phase_1_hidden_controls": [
            "g_cropper_area",
            "g_breederslug_frame",
            "g_breederslug_frame",
            "g_print_paperwork_breeder_button",
            "g_print_paperwork_broker_button",
            "g_print_paperwork_trans_button",
            "g_supplierpidbstatus_frame",
            "g_managepermission_button",
            "g_breederslug_frame",
            "g_employeenotes_groupbox",
            "g_mfgmedia_tab",
            "g_mfgmarketing_tab",
            "g_corp_supplier_frame",
            "g_massactionsupplierinfo_button",
            "g_layaways_tab",
            "g_openorders_tab",
            "g_quotes_select_button",
            "g_tax_exemption_table",
            "g_houseaccount_table",
            "g_houseaccountcontrols_frame",
            # "g_managehouseaccount_button",
            "g_managehouseaccount_button",
            "g_loyalty_group",
            "g_birthday_frame",
            "g_repeatdelivery_massaction_button",
            "g_repeatdelivery_tab",
            "g_emailmarketingsubscription_button",
            "g_savedcards_widget",
            "g_marketingsubsciptions_table",
            # "g_marketingsubscriptioncontrol_frame",
            "g_welcomeemail_button",
            "g_emergencycontacts_tab",
            "g_primaryaddress_button",
            "g_startenddate_repeatdelivery_frame",
            "g_end_start_repeatdelivery_datetimeedit",
            "g_repeat_orders_enddate_label",
            "g_start_repeatdelivery_datetimeedit",
            "g_repeat_order_startdate_label",
            "g_repeat_order_status_label",
            "g_createusername_button",
            "g_enablemarketingsubscription_button",
            "g_diablemarketingsubscription_button",
        ],

        "grp_disabled_phase_1": [
            "g_manageaddress_button",
            "g_deleteaddress_button",
        ],

        "grp_admin_controls": [
            "g_managerole_button",
            "g_managepermission_button"
        ],

        "grp_manager_controls": [
            "g_displayname_lineedit",
            "g_password_lineedit",
            "g_employee_id_lineedit",
            "g_employeerole_combobox",
            "g_employeestatus_combobox",
            # "g_moduleaccess_list",
            "g_locationaccess_list"
        ],

        "grp_clerk_controls": [
            "g_removepaperwork_breeder_button",
            "g_managepaperwork_breeder_button",
            "g_addpaperwork_breeder_button",
            "g_removepaperwork_broker_button",
            "g_managepaperwork_broker_button",
            "g_addpaperwork_broker_button",
            "g_removepaperwork_trans_button",
            "g_managepaperwork_trans_button",
            "g_addpaperwork_trans_button",
        ],

        "grp_manager_admin_tabs": [
        ],

        "grp_command_buttons": [
            "g_cancel_button",
            "g_save_button",
            "g_pagenav_frame",
            "g_managerole_button"
        ],

        "grp_brand_buttons": [
            "g_deletebrand_button",
            "g_managebrand_button"
        ],

        "grp_layaway_buttons": [
            "g_display_layaway_button",
            "g_make_payment_button"
        ],

        "grp_closed_layaway_buttons": [
            "g_display_layaway_button"
        ],

        "grp_opened_layaway_buttons": [
            "g_make_payment_button"
        ],

        "grp_disablemanufacturer_cashier": [
            "g_addressline1_lineedit",
            "g_addressline2_lineedit",
            "g_city_lineedit",
            "g_state_combobox",
            "g_zip_lineedit",
            "g_country_combobox",
            "g_loyaltycard_lineedit",
            "g_phone_listWidget",
            "g_email_listWidget",
            "g_privatenotes_plaintextedit",
            "g_brands_table",
            "g_entityfirst_lineedit",
            "g_entitylast_lineedit",
            "g_business_lineedit",
            # "g_group_checkcombobox",
            "g_accountnumber_lineedit",
            "g_leadtime_lineedit",
            "g_orderfrequency_lineedit",
            "g_ispetsupplier_checkbox",
            "g_minorderamount_lineedit",
            "g_supplierinfosendvia_combobox",
            "g_verification_combobox",
            "g_pidbmappedto_lineedit",
            "g_pidbstatus_lineedit",
            "g_enablepidb_checkBox",
            "g_pidbnote_lineedit",
            "g_suppliernotes_plaintextedit"
        ],

        # All

        "grp_all_hidden_controls": [
            "g_entity_frame",
            "g_business_frame",
            "g_ispetsupplier_checkbox",
            "g_publicnotes_groupbox"
        ],

        "grp_all_hidden_tabs": [
            "g_pictures_widget",
            "g_frequentbuyer_widget",
            "g_purchasehistory_widget",
            "g_employeesettings_widget",
            "g_supplierterms_widget",
            "g_sales_widget",
            "g_breederinfo_widget",
            "g_brokerinfo_widget",
            "g_vetinfo_widget",
            "g_transporterinfo_widget",
            "g_litters_widget",
            "g_brands_widget",
            "g_customfield_widget",
            "g_astroinfo_widget",
            "g_dminfo_widget",
        ],

        # Employee

        "grp_employee_hidden_controls": [
            "g_business_frame"
        ],

        "grp_employee_hidden_tabs": [
            "g_frequentbuyer_widget",
            "g_purchasehistory_widget",
            "g_supplierterms_widget",
            "g_pets_widget",
            "g_sales_widget",
            "g_breederinfo_widget",
            "g_brokerinfo_widget",
            "g_vetinfo_widget",
            "g_transporterinfo_widget",
            "g_litters_widget"
        ],

        "grp_employee_controls": [
            "g_entity_frame",
            # "g_birthday_frame"
        ],

        "grp_employee_tabs": [
            "g_employeesettings_widget"
        ],

        # Customer

        "grp_customer_hidden_controls": [],

        "grp_customer_hidden_tabs": [
            "g_pictures_widget",
            "g_purchasehistory_widget",
            "g_supplierterms_widget",
            "g_breederinfo_widget",
            "g_brokerinfo_widget",
            "g_vetinfo_widget",
            "g_transporterinfo_widget",
            "g_litters_widget"
        ],

        "grp_customer_controls": [
            "g_entity_frame",
            # "g_loyalty_group",
            "g_manageentity_tabwidget",
            "g_publicnotes_groupbox",
            "g_business_frame",
            "g_birthday_frame"
        ],

        "grp_customer_tabs": [
            "g_sales_widget",
            "g_emergencycontacts_tab",
        ],

        "grp_customer_manager_admin_tabs": [
        ],

        "grp_customer_buttons": [
            "g_manage_pet_button"
        ],

        "grp_repeat_delivery": [
            "g_deleterepeatdelivery_button",
            "g_managerepeatdelivery_button"
        ],

        # Manufacturer

        "grp_manufacturer_hidden_controls": [
            "g_entity_frame",
            "g_ispetsupplier_checkbox"
        ],

        "grp_manufacturer_hidden_tabs": [
            "g_pictures_widget",
            "g_frequentbuyer_widget",
            "g_pets_widget",
            "g_sales_widget",
            "g_breederinfo_widget",
            "g_brokerinfo_widget",
            "g_vetinfo_widget",
            "g_transporterinfo_widget",
            "g_litters_widget",
            "g_employeesettings_widget",
            "g_supplierterms_widget"
        ],

        "grp_manufacturer_controls": [
            "g_business_frame"
        ],

        "grp_manufacturer_tabs": [
            "g_brands_widget",
            "g_mfgmedia_tab",
            "g_mfgmarketing_tab"
        ],

        # Supplier

        "grp_supplier_hidden_controls": [
            "g_entity_frame",
            "g_ispetsupplier_checkbox",
            "g_pidb_groupbox"
        ],

        "grp_supplier_hidden_tabs": [
            "g_pictures_widget",
            "g_frequentbuyer_widget",
            "g_pets_widget",
            "g_sales_widget",
            "g_breederinfo_widget",
            "g_brokerinfo_widget",
            "g_vetinfo_widget",
            "g_transporterinfo_widget",
            "g_litters_widget",
            "g_employeesettings_widget"
        ],

        "grp_supplier_controls": [
            "g_business_frame"
        ],

        "grp_supplier_tabs": [
            "g_supplierterms_widget"
        ],

        # Breeder

        "grp_breeder_hidden_controls": [
        ],

        "grp_breeder_hidden_tabs": [
            "g_frequentbuyer_widget",
            "g_purchasehistory_widget",
            "g_supplierterms_widget",
            "g_pets_widget",
            "g_sales_widget"
        ],

        "grp_breeder_controls": [
            "g_entity_frame",
            "g_business_frame"
        ],

        "grp_breeder_tabs": [
            "g_breederinfo_widget",
            "g_pictures_widget"
        ],

        "grp_paperwork_breeder_buttons": [
            "g_print_paperwork_breeder_button", "g_managepaperwork_breeder_button", "g_removepaperwork_breeder_button"
        ],

        # Broker

        "grp_broker_hidden_controls": [
        ],

        "grp_broker_hidden_tabs": [
            "g_frequentbuyer_widget",
            "g_purchasehistory_widget",
            "g_supplierterms_widget",
            "g_pets_widget",
            "g_sales_widget"
        ],

        "grp_broker_controls": [
            "g_entity_frame",
            "g_business_frame"
        ],

        "grp_broker_tabs": [
            "g_brokerinfo_widget",
            "g_pictures_widget"
        ],

        "grp_paperwork_broker_buttons": [
            "g_print_paperwork_broker_button", "g_managepaperwork_broker_button", "g_removepaperwork_broker_button"
        ],

        # Vet

        "grp_vet_hidden_controls": [
        ],

        "grp_vet_hidden_tabs": [
            "g_pictures_widget",
            "g_frequentbuyer_widget",
            "g_purchasehistory_widget",
            "g_supplierterms_widget",
            "g_pets_widget",
            "g_sales_widget"
        ],

        "grp_vet_controls": [
            "g_entity_frame",
            "g_business_frame"
        ],

        "grp_vet_tabs": [
            "g_vetinfo_widget"
        ],

        # Transporter

        "grp_transporter_hidden_controls": [
        ],

        "grp_transporter_hidden_tabs": [
            "g_pictures_widget",
            "g_frequentbuyer_widget",
            "g_purchasehistory_widget",
            "g_supplierterms_widget",
            "g_pets_widget",
            "g_sales_widget"
        ],

        "grp_transporter_controls": [
            "g_entity_frame",
            "g_business_frame"
        ],

        "grp_transporter_tabs": [
            "g_transporterinfo_widget"
        ],

        "grp_paperwork_trans_buttons": [
            "g_print_paperwork_trans_button", "g_managepaperwork_trans_button", "g_removepaperwork_trans_button"
        ],

        # Custom Fields
        "grp_custom_field": [
            "g_removecustomfield_button",
            "g_managecustomfield_button"
        ]
    },

    "form": {
        "title": "Manage People & Businesses",
        "resizable": "true",

        "on_load": [
            "setup_form",
            "prepare_form",
            "prepare_form_subtype",
            "load_data",
            ("push", "$storage.close", "True")
        ],

        "on_close": {
            ("if", "$repeat_orders_timer.running",
                "$repeat_orders_timer.stop_timer"),
        }
    },

    "g_phone_listWidget": {
        "selection_type": "rows",
    },

    "g_email_listWidget": {
        "selection_type": "rows",
    },

    "g_entity_images_list": {
        "selection_type": "rows",
    },

    "g_unified_search_frame": {
        "visible": False
    },

    "g_print_addresslabel_button": {
        "on_release": [
            ("push", "$storage.report_data", "$storage.id"),
            ("if", "$ext.reports.config['printer_labels']",
                ("transfer", "get_address_label",
                    [("transfers", ["get_address_label_report_template", "get_address_report_data"],
                        [(
                            "$ext.reports.print_template",
                            "$ext.reports.config['printer_labels']",
                            "$result[0].flin_contents",
                            "$result[1]"
                        )
                    ])
                ]),
                ("error_message", "Please setup labels and printers in settings screen")
            )
        ]
    },

    "g_print_customercardlabel_button": {
        "on_release": [
            ("push", "$storage.report_data", "$storage.id"),
            ("if", "$ext.reports.config['printer_labels']",
                ("transfer", "get_card_label", [
                    ("transfers", ["get_address_label_report_template", "get_address_report_data"], [
                        (
                            "$ext.reports.print_template",
                            "$ext.reports.config['printer_labels']",
                            "$result[0].flin_contents",
                            "$result[1]"
                        )
                    ])
                ]),
             ("error_message", "Please setup labels and printers in settings screen")
             )
        ]
    },

    "g_managerole_button": {
        "on_release": ("transfer", "select_role")
    },

    "g_addressline1_lineedit": {
        "on_change": [
            ("if", "$g_addressline1_lineedit.data != ''",
                ("change_state", "g_addressline2_lineedit", {"default_state": "enabled"}),
                [
                    "$g_addressline2_lineedit.clear",
                    ("change_state", "g_addressline2_lineedit", {"default_state": "disabled"})
                ])
        ]
    },

    "g_addressline2_lineedit": {
        "blank_ok": "true"
    },

    "g_state_combobox": {
        "clear": False,
        "blank": False,

        "bind": {
            "show": "prgn_iso_code",
            "match": "postal_region_id",
        }
    },

    "g_country_combobox": {
        "clear": True,
        "blank": False,

        "bind": {
            "show": "ctry_full_name",
            "match": "country_id"
        },

        "on_change": [
            ("if", "$g_country_combobox.is_selected", ("transfer", "get_regions"))
        ]
    },

    "g_loyaltycard_lineedit": {
        "blank_ok": "true"
    },

    "c_group_checkcombobox": {
        "status": False,
        "delay": True,
        "bind": {
            "show": "role_name",
            "value": "role_id",
            "check": "role_is_mfd"
        },

        "on_hide_list": "manage_role_controls"
    },

    # Address
    "g_address_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "local_sort": True,
        "sort": ["string", "string", "string", "string", "string", "string", "string", "string", "string", "string"],

        "bind": {
            "show_fields": [
                "$row.name or ''",
                "$row.type or ''",
                "$row.first_name or ''",
                "$row.last_name or ''",
                "$row.business_name or ''",
                "$row.address_line1 + ' ' + $row.address_line2",
                "$row.city",
                # "$row.region_id",
                "$storage.region_ids#where('postal_region_id', $row.region_id).prgn_iso_code",
                "$row.zipcode",
                "$storage.country_ids#where('country_id', $row.country_id).ctry_full_name",

                # "$row.country_id",
            ]
        },

        "on_double_click_row": [
            ("link_to", "g_manageaddress_button", "on_release")
        ],

        "on_row_select": [
            ("change_state", "g_manageaddress_button", {"default_state": "enabled"}),
            ("change_state", "g_deleteaddress_button", {"default_state": "enabled"}),
            ("if", "$g_address_table.all_selected#length > 1",
                ("change_state", "g_primaryaddress_button", {"default_state": "disabled"}),
                ("change_state", "g_primaryaddress_button", {"default_state": "enabled"})),
            "$g_addaddress_button.hide",
            "$g_primaryaddress_button.show",
            # ("change_state", "g_addaddress_button", {"default_state": "disabled"}),
        ],

        "on_selection_clear": [
            ("change_state", "g_manageaddress_button", {"default_state": "disabled"}),
            ("change_state", "g_deleteaddress_button", {"default_state": "disabled"}),
            ("change_state", "g_addaddress_button", {"default_state": "enabled"}),
            "$g_addaddress_button.show",
            "$g_primaryaddress_button.hide",
        ],
    },

    "g_addaddress_button": {
        "on_release": [
            ("if", "$storage.id != ''",
             ("transfer", "add_entity_address"),
             [
                 ("push", "$storage.close", "AddAddress"),
                 "save_entity"
             ])
        ]
    },

    "g_manageaddress_button": {
        "on_release": [
            ("transfer", "update_entity_address")
        ]
    },

    "g_deleteaddress_button": {
        "on_release": [
            ("push", "$storage.address_ids", ("#map", "$g_address_table.all_selected.id", "#dict('id',$_,'deleted',True)")),
            {"confirm": {
                "on_accept": ("transfer", "delete_entity_address")}}
        ]
    },

    "g_primaryaddress_button": {
        "on_release": {
            "confirm": {
                "on_accept": [
                    ("transfer", "primary_entity_address")
                ]

            }
        }
    },

    # House Accounts
    "g_emergencycontact_table": {
        "clear": True,

        "selection_mode": "mutli_selection",
        "sections_movable": False,

        "default_sort_field": "econt_name",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.econt_name",
                "$row.ent_first_name",
                "$row.ent_last_name",
                "($row.ent_contact_numbers#values  if $row.ent_contact_numbers != None and $row.ent_contact_numbers#length != 0) or '",
                "($row.ent_emails#values if $row.ent_emails != None and $row.ent_emails#length != 0) or '",
            ]
        },

        "on_double_click_row": [
             ("link_to", "g_manageemergencycontact_button", "on_release")
        ],

        "on_row_select": [
            ("change_state", "g_addemergencycontact_button", {"default_state": "disabled"}),
            ("change_state", "g_manageemergencycontact_button", {"default_state": "enabled"}),
            ("change_state", "g_deleteemergencycontact_button", {"default_state": "enabled"})
        ],

        "on_selection_clear": [
            ("change_state", "g_addemergencycontact_button", {"default_state": "enabled"}),
            ("change_state", "g_manageemergencycontact_button", {"default_state": "disabled"}),
            ("change_state", "g_deleteemergencycontact_button", {"default_state": "disabled"})
        ],
    },

    "g_addemergencycontact_button": {
        "on_release": [
            ("if", "$storage.id != ''",
                ("transfer", "create_emergency_contact"),
                [
                    ("push", "$storage.close", "AddContact"),
                    "save_entity"
                ])
        ]
    },

    "g_manageemergencycontact_button": {
        "on_release": [
            ("transfer", "update_emergency_contact")
        ]
    },

    "g_deleteemergencycontact_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "delete_emergency_contact")
            }
        }
    },

    # Website Marketing
    "g_marketingsubsciptions_table": {
        "clear": True,

        "selection_mode": "multi",
        "sections_movable": False,

        "default_sort_field": "name",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.name",
                "('Yes' if $row.linked_id in $storage.entity_subscription_ids) or 'No'",
            ]
        }
    },

    # Bonus Bucks
    "g_updatebonusbucks_label": {
        "on_release": [
            ("transfer", "update_bonusbucks")
        ]
    },

    # Tax Certificates
    "g_updatetaxcertificates_label": {
        "on_release": [
            ("if", "$storage.id != ''",
                ("transfer", "update_taxcertificates"),
                [
                    ("push", "$storage.close", "Taxes"),
                    ("save_entity"),
                ])
        ]
    },

    "g_tax_exemption_table": {
        "clear": True,

        "selection_mode": "single",
        "sections_movable": False,

        "default_sort_field": "taxrl_description",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.taxch_description",
                "$row.taxrl_description",
                "$row.taxrl_valid_until"
            ]
        }
    },

    # House Accounts
    "g_houseaccount_table": {
        "clear": True,

        "selection_mode": "single",
        "sections_movable": False,

        "default_sort_field": "hacc_name",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.hacc_name",
                "$row.hacc_balance#currency",
                "$row.hacc_credit_limit#currency"
            ]
        },

        "on_double_click_row": [
            ("if", "$app.permissions.has_role(-8,-9,-19)",
                ("link_to", "g_managehouseaccount_button", "on_release"))
        ],

        "on_row_select": [
            ("if", "$app.permissions.has_role(-8,-9,-19)",
                "$g_managehouseaccount_button.show"),
            "$g_newhouseaccount_button.hide",
            ("change_state", "g_managehouseaccount_button", {"default_state": "enabled"}),
            ("change_state", "g_payhouseaccount_button", {"default_state": "enabled"})
        ],

        "on_selection_clear": [
            ("if", "$app.name == 'Cash Register' and $app.permissions.has_role(-9)",
                [
                    "$g_newhouseaccount_button.show",
                    "$g_managehouseaccount_button.hide"
                ]),

            ("change_state", "g_managehouseaccount_button", {"default_state": "disabled"}),
            ("change_state", "g_payhouseaccount_button", {"default_state": "disabled"})
        ],
    },

    "g_managehouseaccount_button": {
        "on_release": [
            ("transfer", "update_houseaccount")
        ]
    },

    "g_payhouseaccount_button": {
        "on_release": [
            "pay_now",
            "$form.close"
        ]
    },

    "g_newhouseaccount_button": {
        "on_release": [
            ("transfer", "create_houseaccount")
        ]
    },

    # Frequent Buyer
    "g_programstatus_table": {
        "clear": True,

        "selection_mode": "single",
        "sections_movable": False,

        "default_sort_field": "prom_name",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.prom_name or ''",
                ("$row.promfb_bags#quantity or ''", {"halign": "right"}),
                ("$row.prompq_qty#quantity or ''", {"halign": "right"}),
                ("$row.remaining_qty#quantity or ''", {"halign": "right"}),
            ]
        },

        "on_double_click_row": [
             ("if", "$app.permissions.has_permission_other(1, -36) == True",
                {"link_to": ("g_managefrequentBuyer_button", "on_release")}),
        ],

        "on_row_select": [
            ("change_state", "g_managefrequentBuyer_button", {"default_state": "enabled"}),

        ],

        "on_selection_clear": [
            ("change_state", "g_managefrequentBuyer_button", {"default_state": "disabled"}),
        ],
    },

    "g_managefrequentBuyer_button": {
        "on_release": [
            ("transfer", "manage_buyer_program")
        ]
    },

    "g_addfrequentBuyer_button": {
        "on_release": [
            ("transfer", "create_buyer_program")
        ]
    },

    "g_rewardsearned_table": {
        "clear": True,

        "selection_mode": "single",
        "sections_movable": False,

        "default_sort_field": "prom_name",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.prom_name or ''",
                ("$row.rewarded_amount#currency or ''", {"halign": "right"}),
                ("$row.promfb_bags#quantity or ''", {"halign": "right"}),
                ("$row.rewarded_qty#quantity or ''", {"halign": "right"}),
                ("$row.txn_type_number or ''", {"halign": "right"}),
            ]
        },
    },

    # Media
    "g_crop_and_save_btn": {
        "on_release": [
            ("if", "$storage.type == 'image'",
             [
                 ("if", "$cropper.is_updated == True",
                  ("if", "$cropper.area_width < 400",
                   ("error_message",
                    "The cropped image size is too small. Please make sure your saved image size is at least 400 pixels wide."),
                   ("transfer", "create_image")),
                  [
                      ("transfer", "change_image_title")
                  ])
             ],
             ("transfer", "change_video_title")),
        ]
    },

    "g_cropper_cancel_btn": {
        "on_release": [
            "$g_addedit_groupBox.show",
            "$g_cropper_area.hide",
            "$g_crop_and_save_btn.hide",
            "$g_cropper_cancel_btn.hide",

            "g_cropper_cancel_btn.hide",
            ("push", "$g_uploadfilename_lineedit", "")
        ]
    },

    # Purchase History
    "g_purchaseorder_table": {
        # sortable
        # "sections_movable": True
    },

    "g_product_table": {
        # sortable
        # "sections_movable": True
    },

    # Tabs
    "g_manageentity_tabwidget": {
        "on_change": [
            ("if", "$g_manageentity_tabwidget.tab_name == 'g_sales_widget'",
                ("if","$storage.clicked_sales_tab == False",
                    [
                        ("push", "$storage.clicked_sales_tab", True),
                        ("if", "$app.name == 'Cash Register'",
                            [
                                ("transfer", "read_repeat_deliveries"),
                            ]),

                        ("if", "'Customer' in $c_group_checkcombobox.checked.role_name",
                            [
                             "load_product_sales",
                             ("transfers", ["get_layaways", "get_quotes"])
                         ]),
                    ])
            )
        ]
    },

    # Sales
    "g_sales_table": {
        "clear": True,

        "selection_mode": "single_selection",
        "sections_movable": False,

        "bind": {"show_fields": []},

        "on_row_select": [
        ],

        "on_double_click_row": [
        ],

        "on_sort": "load_product_sales"
    },

    "g_exportsales_label": {
        "on_release": [
            ("if", "$g_sales_sumbyperiod_combbox.selected.value == 'events'",
                ("transfer", "print_customer_product_sales_by_event"),
                ("transfer", "print_customer_product_sales_summary"))
        ]
    },

    "g_sales_type_combbox": {
        "blank": False,
        "clear": True,
        "bind": {
            "show": "name",
            "match": "value",
        }
    },

    "g_sales_sumbyperiod_combbox": {
        "blank": False,
        "clear": True,
        "bind": {
            "show": "name",
            "match": "value",
        }
    },

    # Quotes
    "g_quotes_table": {
        "clear": True,

        "selection_mode": "single_selection",
        "sections_movable": True,

        "remote_sort": ["txn_type_number", "customer_full_name", "txn_opened_on",
                        "txn_subtotal", "txn_grand_total", "user_display_name", "txn_reason"],

        "default_sort_field": "txn_type_number",
        "default_sort_order": "desc",

        "bind": {
            "show_fields": [
                "$row.txn_type_number",
                "$row.customer_full_name or ''",
                "$row.txn_opened_on#datetime.strftime('%Y-%m-%d %H:%M:%S')",
                "$row.txn_subtotal#currency",
                "$row.txn_grand_total#currency",
                "$row.txn_reason or ''"
            ],
            "show_rows": {
                "color": "($row.txn_reason == None)#switch('#FF0000','#000000')"
            }
        },

        "on_double_click_row": [
            ("if", "$app.name == 'Cash Register'",
                ("link_to", "g_quotes_select_button", "on_release")),
        ],

        "on_row_select": [
            ("push", {"$form['output']": {"transaction_id": "$g_quotes_table.selected.transaction_id"}}),
            ("change_state", "g_quotes_select_button", {"default_state": "enabled"})
        ],

        "on_selection_clear": ("change_state", "g_quotes_select_button", {"default_state": "disabled"}),

        "on_sort": "load_quotes"
    },

    "g_quotes_select_button": {
        "on_release": [
            "$app.load_invoice($g_quotes_table.selected.transaction_id)",
            "$form.close"
        ]
    },

    # Suppliers
    "g_supplierinfo_table": {
        "clear": True,

        "remote_sort": ["suploc_account_number", "suploc_lead_time_days", "suploc_order_frequency", "suploc_min_order_amount",
                        "suploc_send_po_via", "suploc_receive_verify"],

        "default_sort_field": "suploc_account_number",
        "default_sort_order": "desc",

        "selection_mode": "mutli_selection",
        "sections_movable": False,

        "bind": {
            "show_fields": [
                ("$row.account_number or ''", {"halign": "right"}),
                ("$row.lead_time_days", {"halign": "right"}),
                ("$row.order_frequency#int", {"halign": "right"}),
                ("$row.min_order_amount#float", {"halign": "right"}),
                "$row.send_po_method",
                "$row.receive_verify_behavior",
                "$row.location.report_code",
                "($row.pidb_last_sync_at#date if $row.pidb_last_sync_at !=None) or ''",
                "$row.has_pidb_catalogs",
                "$row.notes or ''"
            ]
        },

        "on_row_select": [
            ("change_state", "g_managesupplierinfo_button", {"default_state": "enabled"}),
            ("if", "$g_supplierinfo_table.all_selected#length < 2",
                "$g_massactionsupplierinfo_button.show",
                "$g_massactionsupplierinfo_button.hide"),
            "$g_addsupplierinfo_button.hide",
            ("change_state", "g_removesupplierinfo_button", {"default_state": "enabled"}),
        ],

        "on_selection_clear": [
            ("change_state", "g_managesupplierinfo_button", {"default_state": "disabled"}),
            "$g_massactionsupplierinfo_button.hide",
            "$g_addsupplierinfo_button.show",
            ("change_state", "g_removesupplierinfo_button", {"default_state": "disabled"}),

        ],

        "on_double_click_row": [
            ("link_to", "g_managesupplierinfo_button", "on_release"),
        ],

        "on_sort": ("transfer", "get_corp_supplier_info")
    },

    "g_removesupplierinfo_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "delete_current_loc_supplier")
            }
        }
    },

    "g_managesupplierinfo_button": {
        "on_release": ("transfer", "update_pidb_loc_supplier")
    },

    "g_massactionsupplierinfo_button": {
        "on_release": ("transfer", "massaction_pidb_loc_supplier")
    },

    "g_addsupplierinfo_button": {
        "on_release": ("transfer", "create_pidb_loc_supplier")
    },

    "g_supplierinfosendvia_combobox": {
        "clear": False,
        "blank": False,

        "bind": {
            "show": "name",
            "match": "value"
        },
    },

    "g_verification_combobox": {
        "clear": False,
        "blank": False,

        "bind": {
            "show": "name",
            "match": "value"
        },
    },

    "g_weightmeasure_combobox": {
        "clear": True,
        "blank": False,

        "bind": {
            "show": "name",
            "match": "value",
        },

        "on_change": [
            ('if', "$g_min_weight_lineedit.data != '' and $g_max_weight_lineedit.data != ''",
                "calculate_weights")
        ]
    },

    # Employee Settings
    "g_employeerole_combobox": {
        "clear": True,

        "bind": {
            "show": "role_name",
            "match": "role_id"
        },

        "on_change": [
            "g_employeerole_combobox_on_change"
        ]
    },

    "g_employeestatus_combobox": {
        "clear": True,
        "blank": False,

        "bind": {
            "show": "name",
            "match": "value"
        },

        "on_change": [
            ("if", "$g_employeestatus_combobox.selected.value == False",
             [
                ("if", "$storage.user_id == $storage.id",
                    [
                        ("info_message", "Cannot disable user you are currently logged in as."),
                        ("push", "$g_employeestatus_combobox.selected", True)
                    ]),
             ]),

        ]
    },

    "g_moduleaccess_list": {
        "clear": True,
        "selection_type": "checkbox",
        "selection_mode": "multi_selection",
        "selection_behavior": "select_rows",

        "bind": {
            "show": "app_name",
            "match": "app_id"
        }
    },

    "g_locationaccess_list": {
        "clear": True,
        "selection_type": "checkbox",
        "selection_mode": "multi_selection",
        "selection_behavior": "select_rows",

        "bind": {
            "show": "ent_name",
            "match": "loc_entity_id"
        }
    },

    "g_managepermission_button": {
        "on_release": [
            ("transfer", "update_permissions")
        ]
    },

    "g_displayname_lineedit": {
        "on_editing_finish": [
            ("transfer", "duplicate_display_name")
        ],
    },

    "g_emplocations_combobox": {
        "clear": True,
        "blank": False,

        "bind": {
            "show": "loc_report_code",
            "match": "loc_entity_id"
        },
    },

    # Pets
    "g_pets_table": {
        "clear": True,

        "remote_sort": ["ptype_name", "custpet_breed_name", "custpet_gender", "plttr_birthdate"],

        "default_sort_field": "ptype_name",
        "default_sort_order": "asc",

        "selection_mode": "single_selection",
        "sections_movable": False,

        "bind": {
            "columns": [
                ("custpet_name", {"default": "Name"}),
                ("custpet_pet_id", {"default": "Pet ID"}),
                ("custpet_breed_name", {"default": "Breed"}),
                ("ptype_name", {"default": "Type"}),
                ("custpet_microchip_numbers_str", {"default": "Microchip"}),
                ("custpet_color_markings", {"default": "Color/Markings"}),
                ("custpet_identifiers", {"default": "Identifiers"}),
                ("custpet_gender", {"default": "Sex"}),
                ("custpet_birthdate", {"default": "Birthdate"}),
            ],
            "show_fields": [
                "$row.custpet_name or ''",
                "$row.custpet_pet_id or ''",
                "$row.custpet_breed_name or ''",
                "$row.ptype_name or ''",
                "$row.custpet_microchip_numbers_str or ''",
                "$row.custpet_color_markings or ''",
                "$row.custpet_identifiers or ''",
                "$row.custpet_gender or ''",
                "$row.custpet_birthdate#datetime.strftime('%Y-%m-%d')"
            ]
        },

        "on_row_select": [
            ("change_state", "g_deletecustomerpet_button", {"default_state": "enabled"}),
            ("change_state", "g_manage_pet_button", {"default_state": "enabled"}),
            ("change_state", "g_addcustomerpet_button", {"default_state": "disabled"}),
        ],

        "on_selection_clear": [
            ("change_state", "g_deletecustomerpet_button", {"default_state": "disabled"}),
            ("change_state", "g_manage_pet_button", {"default_state": "disabled"}),
            ("change_state", "g_addcustomerpet_button", {"default_state": "enabled"}),
        ],

        "on_double_click_row": [
            ("link_to", "g_manage_pet_button", "on_release"),
        ],

        "on_sort": ("transfer", "get_customer_pets")
    },

    "g_manage_pet_button": {
        "on_release": [
            ("transfer", "manage_customer_pet"),
            # ("return", {"event": "manage_product", "app": "Pet Tracker", "data": {"flags": ["update"], "ids": ["$g_pets_table.selected.customer_pet_id"]}})
        ]
    },

    "g_addcustomerpet_button": {
        "on_release": [
            ("if", "$storage.id != ''",
                ("transfer", "create_customer_pet"),
                [
                    ("push", "$storage.close", "AddPet"),
                    "save_entity"
                ])
        ]
    },

    "g_deletecustomerpet_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "delete_customer_pet"),
            }
        }
    },

    # Pets from Breeder
    "g_litters_table": {
        # sortable
        # "sections_movable": True
    },

    # Brands
    "g_brands_table": {
        "clear": True,

        "sort": ["string"],

        "default_sort_field": "mfgbr_brand_name",
        "default_sort_order": "asc",

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "bind": {
            "show_fields": [
                "$row.mfgbr_brand_name"
            ]
        },

        "on_row_select": ("change_state", "grp_brand_buttons", {"default_state": "enabled"}),

        "on_double_click_row": ("link_to", "g_managebrand_button", "on_release")
    },

    "g_deletebrand_button": {
        "on_release": {
            "confirm": {
                "on_accept": "delete_brand_from_table"
            }
        }
    },

    "g_managebrand_button": {
        "on_release": ("transfer", "manage_brand")
    },

    "g_addbrand_button": {
        "on_release": ("transfer", "create_brand")
    },

    # Layaways
    "g_layaways_table": {
        "clear": True,

        "remote_sort": ["txn_type_number", "customer_full_name", "txn_opened_on",
                        "txn_subtotal", "txn_grand_total"],

        "default_sort_field": "txn_type_number",
        "default_sort_order": "desc",

        "selection_mode": "single_selection",
        "sections_movable": False,

        "bind": {
            "show_fields": [
                "$row.txn_type_number",
                "$row.txn_opened_on#datetime.strftime('%Y-%m-%d %H:%M:%S')",
                "$row.txn_expiration_date#datetime.strftime('%Y-%m-%d')",
                ("$row.txn_subtotal#currency", {"halign": "right"}),
                ("$row.txn_grand_total#currency", {"halign": "right"}),
                ("(($row.txn_grand_total or 0)-($row.txnpay_amount_sum or 0))#currency", {"halign": "right"})
            ]
        },

        "on_row_select": [
            ("if", "$g_layaways_table.selected.txn_status == 'Opened'",
                ["$g_display_layaway_button.hide",
                    ("if", "$app.name == 'Cash Register'", "$g_make_payment_button.show")],
                [("if", "$app.name == 'Cash Register'", "$g_display_layaway_button.show"),
                    "$g_make_payment_button.hide"])
        ],

        "on_selection_clear": [
            ("change_state", "grp_layaway_buttons", {"default_state": "disabled"})
        ],

        "on_double_click_row": [
            ("if", "$app.name == 'Cash Register'",
                ("if", "$g_layaways_table.selected.txn_status == 'Opened'",
                    ("link_to", "g_make_payment_button", "on_release"),
                    ("link_to", "g_display_layaway_button", "on_release")))
        ],

        "on_sort": ("transfer", "get_layaways")
    },

    "g_display_layaway_button": {
        "on_release": [
            ("if", "'Opened' in $g_layaways_table.all_selected.txn_status",
                ("info_message", "You can only display Closed or Abandoned invoices."),
                ("transfer", "display_layaway"))
        ]
    },

    "g_make_payment_button": {
        "on_release": ("if", "$g_layaways_table.selected.txn_status in #list('Closed', 'Abandoned')",
                        ("info_message", "You can only make payments to opened invoices."),
                        [("return", {"event": "pay_layaway", "transaction_id": "$g_layaways_table.selected.transaction_id"}),
                         "$form.close"])
    },

    # Pricing
    "g_select_pricinggroup_button": {
        "on_release": ("transfer", "select_pricing_group")
    },

    "g_pricinggroup_combobox": {
        "clear": True,
        "blank": False,

        "bind": {
            "show": "ppg_name",
            "match": "product_pricing_group_id"
        }
    },

    # Breeders
    "g_paperwork_breeder_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": True,

        "remote_sort": ["ppw_paperwork_displayname", "ppwc_name", "ppw_in_print_qty", "ppw_out_print_qty"],

        "default_sort_field": "ppw_paperwork_displayname",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.ppw_paperwork_displayname",
                "$row.ppwc_name",
                ("$row.ppw_in_print_qty#quantity or 0", {"halign": "right"}),
                ("$row.ppw_out_print_qty#quantity or 0", {"halign": "right"})
            ]
        },

        "on_double_click_row": [
            ("if", "$app.permissions.has_role(-9,-11)",
                ("link_to", "g_managepaperwork_breeder_button", "on_release"))
        ],

        "on_row_select": [
            ("if", "'update' in $storage.status",
                [("if", "$app.permissions.has_role(-9,-11)",
                    [("change_state", "grp_paperwork_breeder_buttons", {"default_state": "enabled"}),
                        "$g_addpaperwork_breeder_button.hide",
                        "$g_print_paperwork_breeder_button.show"],
                    ("change_state", "g_print_paperwork_breeder_button", {"default_state": "enabled"}))])
        ],

        "on_selection_clear": [
            ("if", "'update' in $storage.status",
                [("if", "$app.permissions.has_role(-9,-11)",
                    [("change_state", "grp_paperwork_breeder_buttons", {"default_state": "disabled"}),
                        "$g_addpaperwork_breeder_button.show",
                        "$g_print_paperwork_breeder_button.hide"],
                    ("change_state", "g_print_paperwork_breeder_button", {"default_state": "disabled"}))])
        ],

        "on_sort": "load_breeder_paperworks"
    },

    "g_removepaperwork_breeder_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "delete_breeder_paperwork")
            }
        }
    },

    "g_managepaperwork_breeder_button": {
        "on_release": ("transfer", "update_breeder_paperwork")
    },

    "g_addpaperwork_breeder_button": {
        "on_release": ("transfer", "create_breeder_paperwork")
    },

    "g_print_paperwork_breeder_button": {
        "on_release": "print_paperwork_breeder_reports"
    },

    "g_usda_lineedit": {
        "on_editing_finish": [
            ("push", "$storage.trim", "$g_usda_lineedit.data.strip('\n')"),
            ("push", "$g_usda_lineedit", "$storage.trim"),
            {"validate": {
                "group_name": "g_usda_lineedit_val",
                "on_success": [],
                "on_failure": ("error_message", "'\n'.join($errors)")
            }},
        ]
    },

    # Brokers
    "g_paperwork_broker_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": True,

        "remote_sort": ["ppw_paperwork_displayname", "ppwc_name", "ppw_in_print_qty", "ppw_out_print_qty"],

        "default_sort_field": "ppw_paperwork_displayname",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.ppw_paperwork_displayname",
                "$row.ppwc_name",
                ("$row.ppw_in_print_qty#quantity or 0", {"halign": "right"}),
                ("$row.ppw_out_print_qty#quantity or 0", {"halign": "right"})
            ]
        },

        "on_double_click_row": [
            ("if", "$app.permissions.has_role(-9,-11)",
                ("link_to", "g_managepaperwork_broker_button", "on_release"))
        ],

        "on_row_select": [
            ("if", "'update' in $storage.status",
                [("if", "$app.permissions.has_role(-9,-11)",
                    [("change_state", "grp_paperwork_broker_buttons", {"default_state": "enabled"}),
                        "$g_addpaperwork_broker_button.hide",
                        "$g_print_paperwork_broker_button.show"],
                    ("change_state", "g_print_paperwork_broker_button", {"default_state": "enabled"}))])
        ],

        "on_selection_clear": [
            ("if", "'update' in $storage.status",
                [("if", "$app.permissions.has_role(-9,-11)",
                    [("change_state", "grp_paperwork_broker_buttons", {"default_state": "disabled"}),
                        "$g_addpaperwork_broker_button.show",
                        "$g_print_paperwork_broker_button.hide"],
                    ("change_state", "g_print_paperwork_broker_button", {"default_state": "disabled"}))])
        ],

        "on_sort": "load_broker_paperworks"
    },

    "g_removepaperwork_broker_button": {
        "on_release": {
           "confirm": {
               "on_accept": ("transfer", "delete_broker_paperwork")
           }
        }
    },

    "g_managepaperwork_broker_button": {
        "on_release": ("transfer", "update_broker_paperwork", "broker")
    },

    "g_addpaperwork_broker_button": {
        "on_release": ("transfer", "create_broker_paperwork", "broker")
    },

    "g_print_paperwork_broker_button": {
        "on_release": [
            "print_paperwork_broker_reports"
        ]
    },

    "g_broker_usda_lineedit": {
        "on_editing_finish": [
            ("push", "$storage.trim", "g_broker_usda_lineedit.data.strip('\n')"),
            ("push", "g_broker_usda_lineedit", "$storage.trim"),
            {"validate": {
                "group_name": "g_usda_lineedit_broker_val",
                "on_success": [],
                "on_failure": ("error_message", "'\n'.join($errors)")
            }}
        ]
    },

    # Transporters
    "g_paperwork_trans_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": True,

        "remote_sort": ["ppw_paperwork_displayname", "ppwc_name", "ppw_in_print_qty", "ppw_out_print_qty"],

        "default_sort_field": "ppw_paperwork_displayname",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.ppw_paperwork_displayname",
                "$row.ppwc_name",
                ("$row.ppw_in_print_qty#quantity or 0", {"halign": "right"}),
                ("$row.ppw_out_print_qty#quantity or 0", {"halign": "right"})
            ]
        },

        "on_double_click_row": [
            ("if", "$app.permissions.has_role(-9,-11)",
                ("link_to", "g_managepaperwork_trans_button", "on_release"))
        ],

        "on_row_select": [
            ("if", "'update' in $storage.status",
                [("if", "$app.permissions.has_role(-9,-11)",
                    [("change_state", "grp_paperwork_trans_buttons", {"default_state": "enabled"}),
                        "$g_addpaperwork_trans_button.hide",
                        "$g_print_paperwork_trans_button.show"],
                    ("change_state", "g_print_paperwork_trans_button", {"default_state": "enabled"}))])
        ],

        "on_selection_clear": [
            ("if", "'update' in $storage.status",
                [("if", "$app.permissions.has_role(-9,-11)",
                    [("change_state", "grp_paperwork_trans_buttons", {"default_state": "disabled"}),
                        "$g_addpaperwork_trans_button.show",
                        "$g_print_paperwork_trans_button.hide"],
                    ("change_state", "g_print_paperwork_trans_button", {"default_state": "disabled"}))])
        ],

        "on_sort": "load_trans_paperworks"
    },

    "g_removepaperwork_trans_button": {
        "on_release": {
           "confirm": {
               "on_accept": ("transfer", "delete_trans_paperwork")
           }
        }
    },

    "g_managepaperwork_trans_button": {
        "on_release": ("transfer", "update_trans_paperwork", "transporter")
    },

    "g_addpaperwork_trans_button": {
        "on_release": ("transfer", "create_trans_paperwork", "transporter")
    },

    "g_print_paperwork_trans_button": {
        "on_release": [
            "print_paperwork_trans_reports"
        ]
    },

    "g_transporter_usda_lineedit": {
        "on_editing_finish": [
            {"validate": {
                "group_name": "g_usda_lineedit_transporter_val",
                "on_success": [],
                "on_failure": ("error_message", "'\n'.join($errors)")
            }},
        ]
    },

    # Custom Fields Tab
    "g_customfield_table": {
        "clear": True,

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "remote_sort": ["key", "value"],

        "default_sort_field": "key",
        "default_sort_order": "asc",

        "bind": {
            "show_fields": [
                "$row.pbrd_cust_field_key or ''",
                "$row.pbrd_cust_field_value.replace('[', '').replace(']', '').replace('\"', '') or ''"
            ]
        },

        "on_row_select": [
            ("change_state", "g_addcustom_button", {"default_state": "disabled"}),
            ("change_state", "grp_custom_field", {"default_state": "enabled"})
        ],

        "on_selection_clear": [
            ("change_state", "g_addcustom_button", {"default_state": "enabled"}),
            ("change_state", "grp_custom_field", {"default_state": "disabled"})
        ],

        "on_double_click_row": ("link_to", "g_managecustomfield_button", "on_release"),

        "on_url_link_click": "open_url",

        "on_sort": ("transfer", "get_custom_fields")
    },

    "g_managecustomfield_button": {
        "on_release": [
            ("transfer", "update_customfield")
        ]
    },

    "g_addcustom_button": {
        "on_release": ("transfer", "add_custom_field")
    },

    "g_removecustomfield_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "delete_custom_field")
            }
        }
    },

    # Repeat Delveries
    "g_repeatdelivery_table": {
        "clear": True,

        "remote_sort": ["product_id", "prod_barcode", "prod_name", "custrs_qty", "prdpr_price", "custrs_delivery_type",
                        "custrs_frequency", "custrs_event_date", "loc_report_code"],

        "default_sort_field": "custrs_next_order ",
        "default_sort_order": "asc",

        "selection_mode": "multi_selection",
        "sections_movable": False,

        "bind": {

            "columns": [
                ("product_id", {"default": "SKU"}),
                ("prod_barcode", {"default": "Barcode"}),
                ("prod_name", {"default": "Description"}),
                ("custrs_qty", {"default": "Qty", "halign": "right"}),
                ("prdpr_price", {"default": "Price", "halign": "right"}),
                ("custrs_delivery_type", {"default": "Delivery Type"}),
                ("frequency_text", {"default": "Frequency"}),
                ("custrs_event_date", {"default": "Next Delivery"}),
                ("loc_report_code", {"default": "Location"}), #, "view": ["corp"]})
            ],

            "show_fields": [
                "$row.product_id ",
                "$row.prod_barcode",
                "$row.prod_name",
                ("$row.custrs_qty#quantity", {"halign": "right"}),
                ("$row.prdpr_price#currency", {"halign": "right"}),
                "$row.custrs_delivery_type",
                "$row.frequency_text or ''",
                "($row.custrs_event_date if $row.custrs_event_date != None) or ''",
                "$row.loc_report_code"
            ],

            "show_rows": {
                "color": "($row.custrs_disabled == True)#switch('#FF0000','#000000')"
            }

        },

        "on_row_select": [
            ("change_state", "g_deleterepeatdelivery_button", {"default_state": "enabled"}),
            ("change_state", "g_managerepeatdelivery_button", {"default_state": "enabled"}),
            ("change_state", "g_addrepeatdelivery_button", {"default_state": "disabled"})
        ],

        "on_selection_clear": [
            "$g_repeatdelivery_massaction_button.hide",
            "$g_addrepeatdelivery_button.show",

            ("change_state", "g_deleterepeatdelivery_button", {"default_state": "disabled"}),
            ("change_state", "g_managerepeatdelivery_button", {"default_state": "disabled"}),
            ("change_state", "g_addrepeatdelivery_button", {"default_state": "enabled"})
        ],

        "on_double_click_row": [
            ("link_to", "g_managerepeatdelivery_button", "on_release")
        ],

        "on_sort": ("transfer", "read_repeat_deliveries")
    },

    "g_location_repeat_orders_combobox": {
        "clear": True,
        "blank": False,
        "bind": {
            "show": "loc_report_code",
            "match": "loc_entity_id"
        },

        "on_change": [
            ("if", "$storage.loading == False",
                ("transfer", "read_repeat_deliveries")),
        ]
    },

    "g_deleterepeatdelivery_button": {
        "on_release": {
            "confirm": {
                "on_accept": ("transfer", "delete_repeatdeliveries")
            }
        }
    },

    "g_managerepeatdelivery_button": {
        "on_release": ("transfer", "update_repeatdeliveries")
    },

    "g_addrepeatdelivery_button": {
        "on_release": "check_address_repeatdelivery"
    },

    "g_repeatdelivery_massaction_button": {
        "on_release": ("transfer", "massaction_repeatdeliveries")
    },

    "g_featured_checkbox": {
        "on_change": [
            ("if", "$g_featured_checkbox.data == True",
             ("push", "$g_public_checkbox", True))
        ]
    },

    "g_public_checkbox": {
        "on_change": [
            ("if", "$g_public_checkbox.data == False",
             ("push", "$g_featured_checkbox", False))
        ]
    },

    # Reset Website Password
    "g_resetpassword_button": {
        "on_release": [
            ("if", "$storage.websites#length > 1",
                ("transfer", "reset_webpassword_dialog"),
                ("transfer", "reset_webpassword"))
        ]
    },

    "g_welcomeemail_button": {
        "on_release": [
            ("transfer", "send_welecome_email")
        ]
    },

    "g_webusername_lineedit": {
        "on_change": [
            ("if", "$g_webusername_lineedit.data#length > 0",
                ("change_state", "g_resetpassword_button", {"default_state": "enabled"}),
                ("change_state", "g_resetpassword_button", {"default_state": "disabled"}),)
        ]
    },

    "g_createusername_button": {
        "on_release": [
            "create_username"
        ]
    },

    # Command buttons
    "g_cancel_button": {
        "on_release": [
            ("push", "$storage.sender", "Cancel"),
            "cancel_entity"
        ]
    },

    "g_save_button": {
        "on_release": [
            ("push", "$storage.sender", "Save"),
            "save_entity"
        ]
    },
}

# Merge included specs to main
for spec in include_specs:
    fpe_keys = ["fpe_actions", "fpe_transfers", "fpe_custom_widgets", "fpe_validations", "fpe_groups"]
    for key, value in spec.items():
        if key in fpe_keys:
            assert isinstance(value, type(fpe_specs[key])), "fpe_specs types do not match"
            if isinstance(value, dict):
                fpe_specs[key].update(**value)
            elif isinstance(value, list):
                fpe_specs[key].extend(value)
        else:
            fpe_specs[key] = value
