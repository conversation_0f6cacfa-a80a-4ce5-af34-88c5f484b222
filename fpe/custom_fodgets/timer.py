from fpe.common import *
from . import CustomFodget


class Simple_Timer(CustomFodget):
    typename = "simple_timer"

    def __init__(self, controller, params):
        super().__init__(controller, params)

        self.timer = QTimer()
        self.timer.timeout.connect(self.run)

        self.refresh = params.get("refresh")
        self.minute = params.get("minutes") or 3

    # ===========================================================================
    #    Fodget Interface
    # ===========================================================================

    def start_timer(self):
        self.timer.start((int(self.minute*60))*1000)

    def stop_timer(self):
        self.timer.stop()

    @property
    def running(self):
        return self.timer.isActive()

    def run(self):
        self.execute_action(self.refresh)
        self.start_timer()

